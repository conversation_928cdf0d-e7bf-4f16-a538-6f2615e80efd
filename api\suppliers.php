<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class SupplierAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function getSuppliers() {
        try {
            $sql = "SELECT * FROM suppliers WHERE is_active = 1 ORDER BY created_at DESC";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $suppliers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $totalDebt = 0;
            foreach ($suppliers as $supplier) {
                $totalDebt += $supplier['balance'];
            }

            return [
                'success' => true,
                'data' => $suppliers,
                'summary' => [
                    'total_suppliers' => count($suppliers),
                    'total_debt' => $totalDebt,
                    'new_suppliers_this_month' => $this->getNewSuppliersThisMonth()
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
            ];
        }
    }

    public function addSupplier($data) {
        try {
            $sql = "INSERT INTO suppliers (name, phone, city, address, balance, code, created_at) 
                    VALUES (:name, :phone, :city, :address, :balance, :code, NOW())";
            
            $stmt = $this->conn->prepare($sql);
            $code = 'S' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':city', $data['city']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':balance', $data['balance']);
            $stmt->bindParam(':code', $code);
            
            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة المورد بنجاح',
                    'supplier_id' => $this->conn->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة المورد'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
            ];
        }
    }

    public function updateSupplier($id, $data) {
        try {
            $sql = "UPDATE suppliers SET 
                    name = :name, 
                    phone = :phone, 
                    city = :city, 
                    address = :address, 
                    balance = :balance,
                    updated_at = NOW()
                    WHERE id = :id";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':phone', $data['phone']);
            $stmt->bindParam(':city', $data['city']);
            $stmt->bindParam(':address', $data['address']);
            $stmt->bindParam(':balance', $data['balance']);
            
            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث بيانات المورد بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث بيانات المورد'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
            ];
        }
    }

    public function deleteSupplier($id) {
        try {
            $sql = "UPDATE suppliers SET is_active = 0 WHERE id = :id";
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            
            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'تم حذف المورد بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في حذف المورد'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
            ];
        }
    }

    public function searchSuppliers($query) {
        try {
            $sql = "SELECT * FROM suppliers 
                    WHERE is_active = 1 AND (
                        name LIKE :query OR 
                        phone LIKE :query OR 
                        city LIKE :query OR 
                        code LIKE :query
                    ) 
                    ORDER BY created_at DESC";
            
            $stmt = $this->conn->prepare($sql);
            $searchTerm = '%' . $query . '%';
            $stmt->bindParam(':query', $searchTerm);
            $stmt->execute();
            
            $suppliers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'data' => $suppliers
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في البحث: ' . $e->getMessage()
            ];
        }
    }

    private function getNewSuppliersThisMonth() {
        try {
            $sql = "SELECT COUNT(*) as count FROM suppliers 
                    WHERE is_active = 1 AND 
                    MONTH(created_at) = MONTH(CURRENT_DATE()) AND 
                    YEAR(created_at) = YEAR(CURRENT_DATE())";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result['count'];
        } catch (PDOException $e) {
            return 0;
        }
    }
}

// معالجة الطلبات
$api = new SupplierAPI();
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['search'])) {
            echo json_encode($api->searchSuppliers($_GET['search']));
        } else {
            echo json_encode($api->getSuppliers());
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        echo json_encode($api->addSupplier($input));
        break;
        
    case 'PUT':
        $input = json_decode(file_get_contents('php://input'), true);
        $id = $_GET['id'] ?? null;
        if ($id) {
            echo json_encode($api->updateSupplier($id, $input));
        } else {
            echo json_encode(['success' => false, 'message' => 'معرف المورد مطلوب']);
        }
        break;
        
    case 'DELETE':
        $id = $_GET['id'] ?? null;
        if ($id) {
            echo json_encode($api->deleteSupplier($id));
        } else {
            echo json_encode(['success' => false, 'message' => 'معرف المورد مطلوب']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}
?>