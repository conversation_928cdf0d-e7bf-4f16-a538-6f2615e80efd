<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>إدارة العملاء</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --bg: linear-gradient(135deg, #f0f7ff, #fefefe);
      --card-bg: rgba(255, 255, 255, 0.75);
      --primary: #2563eb;
      --accent: #f59e0b;
      --muted: #6b7280;
      --success: #10b981;
      --danger: #ef4444;
      --shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    }
    body {
      font-family: 'Tajawal', sans-serif;
      margin: 0;
      background: var(--bg);
      padding: 20px;
    }
    .card {
      background: var(--card-bg);
      backdrop-filter: blur(12px);
      border-radius: 20px;
      box-shadow: var(--shadow);
      padding: 16px;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    }
    h1 {
      margin: 0 0 20px;
    }
    .topbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      flex-wrap: wrap;
      gap: 10px;
    }
    .btn {
      background: var(--primary);
      color: #fff;
      border-radius: 12px;
      padding: 10px 14px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: background 0.2s ease;
    }
    .btn:hover {
      background: #1e40af;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }
    th, td {
      padding: 12px 8px;
      text-align: right;
      border-bottom: 1px solid #e5e7eb;
    }
    th {
      color: var(--muted);
      font-weight: 700;
      font-size: 13px;
    }
    .actions button {
      background: transparent;
      border: none;
      cursor: pointer;
      font-size: 16px;
      padding: 4px;
      border-radius: 6px;
      transition: background 0.2s;
    }
    .actions button:hover {
      background: rgba(0,0,0,0.05);
    }
  </style>
</head>
<body>

  <div class="card">
    <div class="topbar">
      <h1>قائمة العملاء</h1>
      <button class="btn" onclick="addClient()">+ إضافة عميل</button>
    </div>

    <table>
      <thead>
        <tr>
          <th>الاسم</th>
          <th>رقم الهاتف</th>
          <th>الدين</th>
          <th>إجراءات</th>
        </tr>
      </thead>
      <tbody id="clients">
        <tr>
          <td>محمد العروي</td>
          <td>0612345678</td>
          <td>1,200 د.م</td>
          <td class="actions">
            <button title="عرض">🔍</button>
            <button title="تعديل">✏️</button>
            <button title="حذف" onclick="deleteClient(this)">🗑️</button>
          </td>
        </tr>
        <tr>
          <td>أحمد التازي</td>
          <td>0678912345</td>
          <td>0 د.م</td>
          <td class="actions">
            <button title="عرض">🔍</button>
            <button title="تعديل">✏️</button>
            <button title="حذف" onclick="deleteClient(this)">🗑️</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <script>
    function addClient() {
      const name = prompt("اسم العميل:");
      if (!name) return;
      const phone = prompt("رقم الهاتف:");
      const debt = prompt("الدين (د.م):", "0");

      const tbody = document.getElementById("clients");
      const tr = document.createElement("tr");
      tr.innerHTML = `
        <td>${name}</td>
        <td>${phone}</td>
        <td>${debt} د.م</td>
        <td class="actions">
          <button title="عرض">🔍</button>
          <button title="تعديل">✏️</button>
          <button title="حذف" onclick="deleteClient(this)">🗑️</button>
        </td>
      `;
      tbody.appendChild(tr);
    }

    function deleteClient(btn) {
      if (confirm("هل تريد حذف هذا العميل؟")) {
        btn.closest("tr").remove();
      }
    }
  </script>

</body>
</html>
