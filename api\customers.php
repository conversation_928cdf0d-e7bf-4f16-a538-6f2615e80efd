<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class CustomerAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    // جلب جميع العملاء
    public function getCustomers() {
        try {
            $sql = "SELECT * FROM customers WHERE is_active = 1 ORDER BY created_at DESC";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // حساب الإحصائيات
            $totalCustomers = count($customers);
            $totalDebt = 0;
            $newCustomersThisMonth = 0;
            $currentMonth = date('Y-m');

            foreach ($customers as $customer) {
                $totalDebt += $customer['balance'];
                if (strpos($customer['created_at'], $currentMonth) === 0) {
                    $newCustomersThisMonth++;
                }
            }

            return [
                'success' => true,
                'data' => $customers,
                'summary' => [
                    'total_customers' => $totalCustomers,
                    'total_debt' => $totalDebt,
                    'new_customers_this_month' => $newCustomersThisMonth
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب العملاء: ' . $e->getMessage()
            ];
        }
    }

    // جلب عميل واحد
    public function getCustomer($id) {
        try {
            $sql = "SELECT * FROM customers WHERE id = ? AND is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            $customer = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($customer) {
                // جلب آخر المعاملات
                $transactionsSql = "SELECT 'sale' as type, invoice_number, invoice_date, total_amount, payment_status
                                   FROM sales_invoices 
                                   WHERE customer_id = ? 
                                   ORDER BY invoice_date DESC 
                                   LIMIT 10";
                $transactionsStmt = $this->conn->prepare($transactionsSql);
                $transactionsStmt->execute([$id]);
                $transactions = $transactionsStmt->fetchAll(PDO::FETCH_ASSOC);

                $customer['recent_transactions'] = $transactions;

                return [
                    'success' => true,
                    'data' => $customer
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب العميل: ' . $e->getMessage()
            ];
        }
    }

    // إضافة عميل جديد
    public function addCustomer($data) {
        try {
            // التحقق من البيانات المطلوبة
            if (empty($data['name'])) {
                return [
                    'success' => false,
                    'message' => 'اسم العميل مطلوب'
                ];
            }

            // إنشاء كود العميل تلقائياً
            $code = $this->generateCustomerCode();

            $sql = "INSERT INTO customers (code, name, phone, email, address, city, balance, credit_limit) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $code,
                $data['name'],
                $data['phone'] ?? '',
                $data['email'] ?? '',
                $data['address'] ?? '',
                $data['city'] ?? '',
                $data['balance'] ?? 0,
                $data['credit_limit'] ?? 0
            ]);

            if ($result) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة العميل بنجاح',
                    'customer_id' => $this->conn->lastInsertId(),
                    'customer_code' => $code
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة العميل'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إضافة العميل: ' . $e->getMessage()
            ];
        }
    }

    // تحديث عميل
    public function updateCustomer($id, $data) {
        try {
            // التحقق من وجود العميل
            $checkSql = "SELECT id FROM customers WHERE id = ? AND is_active = 1";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$id]);
            
            if (!$checkStmt->fetch()) {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }

            $sql = "UPDATE customers SET 
                    name = ?, phone = ?, email = ?, address = ?, city = ?, 
                    credit_limit = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $data['name'],
                $data['phone'] ?? '',
                $data['email'] ?? '',
                $data['address'] ?? '',
                $data['city'] ?? '',
                $data['credit_limit'] ?? 0,
                $id
            ]);

            if ($result) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث العميل'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في تحديث العميل: ' . $e->getMessage()
            ];
        }
    }

    // حذف عميل (حذف منطقي)
    public function deleteCustomer($id) {
        try {
            // التحقق من عدم وجود معاملات للعميل
            $checkSql = "SELECT COUNT(*) as count FROM sales_invoices WHERE customer_id = ?";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$id]);
            $result = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($result['count'] > 0) {
                return [
                    'success' => false,
                    'message' => 'لا يمكن حذف العميل لوجود معاملات مرتبطة به'
                ];
            }

            $sql = "UPDATE customers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'message' => 'تم حذف العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود أو تم حذفه مسبقاً'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في حذف العميل: ' . $e->getMessage()
            ];
        }
    }

    // البحث في العملاء
    public function searchCustomers($query) {
        try {
            $sql = "SELECT * FROM customers 
                    WHERE is_active = 1 AND (
                        name LIKE ? OR 
                        code LIKE ? OR 
                        phone LIKE ? OR
                        city LIKE ?
                    )
                    ORDER BY name";
            
            $searchTerm = "%$query%";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => $customers,
                'count' => count($customers)
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في البحث: ' . $e->getMessage()
            ];
        }
    }

    // جلب العملاء المدينين
    public function getDebtorCustomers() {
        try {
            $sql = "SELECT * FROM customers 
                    WHERE is_active = 1 AND balance > 0
                    ORDER BY balance DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $totalDebt = array_sum(array_column($customers, 'balance'));

            return [
                'success' => true,
                'data' => $customers,
                'count' => count($customers),
                'total_debt' => $totalDebt
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب العملاء المدينين: ' . $e->getMessage()
            ];
        }
    }

    // تحديث رصيد العميل
    public function updateCustomerBalance($id, $amount, $type = 'add') {
        try {
            $operator = ($type === 'add') ? '+' : '-';
            
            $sql = "UPDATE customers SET 
                    balance = balance $operator ?, 
                    updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ? AND is_active = 1";
            
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([abs($amount), $id]);

            if ($result && $stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث رصيد العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث رصيد العميل'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في تحديث رصيد العميل: ' . $e->getMessage()
            ];
        }
    }

    // إنشاء كود العميل
    private function generateCustomerCode() {
        $sql = "SELECT MAX(CAST(SUBSTRING(code, 2) AS UNSIGNED)) as max_num FROM customers WHERE code LIKE 'C%'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $nextNum = ($result['max_num'] ?? 0) + 1;
        return 'C' . str_pad($nextNum, 4, '0', STR_PAD_LEFT);
    }

    // جلب تقرير العميل
    public function getCustomerReport($id, $startDate = null, $endDate = null) {
        try {
            // معلومات العميل
            $customerSql = "SELECT * FROM customers WHERE id = ? AND is_active = 1";
            $customerStmt = $this->conn->prepare($customerSql);
            $customerStmt->execute([$id]);
            $customer = $customerStmt->fetch(PDO::FETCH_ASSOC);

            if (!$customer) {
                return [
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ];
            }

            // بناء شرط التاريخ
            $dateCondition = "";
            $params = [$id];
            
            if ($startDate && $endDate) {
                $dateCondition = "AND invoice_date BETWEEN ? AND ?";
                $params[] = $startDate;
                $params[] = $endDate;
            }

            // فواتير المبيعات
            $salesSql = "SELECT invoice_number, invoice_date, total_amount, paid_amount, payment_status
                        FROM sales_invoices 
                        WHERE customer_id = ? $dateCondition
                        ORDER BY invoice_date DESC";
            
            $salesStmt = $this->conn->prepare($salesSql);
            $salesStmt->execute($params);
            $sales = $salesStmt->fetchAll(PDO::FETCH_ASSOC);

            // المدفوعات
            $paymentsSql = "SELECT payment_number, payment_date, amount, payment_method, notes
                           FROM payments 
                           WHERE customer_id = ? $dateCondition
                           ORDER BY payment_date DESC";
            
            $paymentsStmt = $this->conn->prepare($paymentsSql);
            $paymentsStmt->execute($params);
            $payments = $paymentsStmt->fetchAll(PDO::FETCH_ASSOC);

            // حساب المجاميع
            $totalSales = array_sum(array_column($sales, 'total_amount'));
            $totalPaid = array_sum(array_column($payments, 'amount'));
            $remainingBalance = $totalSales - $totalPaid;

            return [
                'success' => true,
                'data' => [
                    'customer' => $customer,
                    'sales' => $sales,
                    'payments' => $payments,
                    'summary' => [
                        'total_sales' => $totalSales,
                        'total_paid' => $totalPaid,
                        'remaining_balance' => $remainingBalance,
                        'sales_count' => count($sales),
                        'payments_count' => count($payments)
                    ]
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب تقرير العميل: ' . $e->getMessage()
            ];
        }
    }
}

// معالجة الطلبات
$customerAPI = new CustomerAPI();
$method = $_SERVER['REQUEST_METHOD'];
$response = [];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            if (isset($_GET['report'])) {
                $startDate = $_GET['start_date'] ?? null;
                $endDate = $_GET['end_date'] ?? null;
                $response = $customerAPI->getCustomerReport($_GET['id'], $startDate, $endDate);
            } else {
                $response = $customerAPI->getCustomer($_GET['id']);
            }
        } elseif (isset($_GET['search'])) {
            $response = $customerAPI->searchCustomers($_GET['search']);
        } elseif (isset($_GET['debtors'])) {
            $response = $customerAPI->getDebtorCustomers();
        } else {
            $response = $customerAPI->getCustomers();
        }
        break;
        
    case 'POST':
        $data = json_decode(file_get_contents('php://input'), true);
        if (isset($_GET['id']) && isset($_GET['balance'])) {
            $type = $_GET['type'] ?? 'add';
            $response = $customerAPI->updateCustomerBalance($_GET['id'], $data['amount'], $type);
        } else {
            $response = $customerAPI->addCustomer($data);
        }
        break;
        
    case 'PUT':
        if (isset($_GET['id'])) {
            $data = json_decode(file_get_contents('php://input'), true);
            $response = $customerAPI->updateCustomer($_GET['id'], $data);
        } else {
            $response = ['success' => false, 'message' => 'معرف العميل مطلوب'];
        }
        break;
        
    case 'DELETE':
        if (isset($_GET['id'])) {
            $response = $customerAPI->deleteCustomer($_GET['id']);
        } else {
            $response = ['success' => false, 'message' => 'معرف العميل مطلوب'];
        }
        break;
        
    default:
        $response = ['success' => false, 'message' => 'طريقة غير مدعومة'];
        break;
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>