---
description: Repository Information Overview
alwaysApply: true
---

# نظام إدارة المخزون والمبيعات Information

## Summary
نظام شامل لإدارة المخزون والمبيعات مصمم خصيصاً للشركات الصغيرة والمتوسطة. يوفر النظام واجهة مستخدم متجاوبة لإدارة المنتجات، العملاء، الموردين، الفواتير والتقارير. يتميز بلوحة تحكم رئيسية تعرض إحصائيات فورية للمبيعات والمخزون.

## Structure
- **api/**: واجهات برمجة التطبيقات (API) للتعامل مع البيانات
- **assets/**: الملفات الثابتة (CSS, JavaScript)
- **config/**: ملفات الإعداد والتكوين
- **index.html**: الصفحة الرئيسية للتطبيق
- **login.html**: صفحة تسجيل الدخول
- **setup.php**: معالج تهيئة النظام وإعداد قاعدة البيانات
- **start-server.bat**: سكريبت لتشغيل الخادم المحلي

## Language & Runtime
**Language**: PHP
**Version**: 7.4 أو أحدث
**Database**: MySQL 5.7 أو أحدث
**Frontend**: HTML5, CSS3, JavaScript
**Character Encoding**: UTF-8 (دعم اللغة العربية)

## Dependencies
**PHP Extensions**:
- PDO
- PDO_MySQL
- JSON
- MBString

**Frontend Libraries**:
- Chart.js (للرسوم البيانية)
- Font Awesome (للأيقونات)

## Build & Installation
### الطريقة الأولى: استخدام الخادم المدمج في PHP
```bash
# تشغيل الخادم المحلي
php -S localhost:8000
# أو استخدام الملف التنفيذي
start-server.bat
```

### الطريقة الثانية: استخدام XAMPP/WAMP
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r * /var/www/html/inventory-system/
```

### إعداد قاعدة البيانات
```bash
# الوصول إلى معالج التثبيت
http://localhost:8000/setup.php
```

## Database
**Engine**: MySQL 5.7+
**Character Set**: utf8mb4
**Collation**: utf8mb4_unicode_ci
**Tables**:
- users: إدارة المستخدمين والصلاحيات
- products: المنتجات والمخزون
- categories: فئات المنتجات
- customers: بيانات العملاء
- suppliers: بيانات الموردين
- sales_invoices: فواتير المبيعات
- purchase_invoices: فواتير المشتريات
- payments: المدفوعات والتحصيلات
- settings: إعدادات النظام

## API Endpoints
**Products API**: `/api/products.php`
- GET: جلب المنتجات
- POST: إضافة منتج
- PUT: تحديث منتج
- DELETE: حذف منتج

**Customers API**: `/api/customers.php`
- GET: جلب العملاء
- POST: إضافة عميل
- PUT: تحديث بيانات عميل
- DELETE: حذف عميل

**Categories API**: `/api/categories.php`
- GET: جلب الفئات
- POST: إضافة فئة
- PUT: تحديث فئة
- DELETE: حذف فئة

## Testing
**Login Credentials**:
- Username: `admin`
- Password: `admin123`

**Test Data**:
- فئات افتراضية: إلكترونيات، ملابس، أدوات منزلية، كتب وقرطاسية
- إعدادات افتراضية: اسم الشركة، العملة، معدل الضريبة

## Features Status
**Completed**:
- لوحة التحكم الرئيسية
- إدارة المخزون (إضافة، عرض، بحث)
- إدارة العملاء (إضافة، عرض، بحث)
- تسجيل الدخول والخروج
- واجهة مستخدم متجاوبة

**In Development**:
- إدارة الموردين
- نظام الفواتير
- التقارير المتقدمة
- إدارة المستخدمين
- نظام الصلاحيات