<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهيئة نظام إدارة المخزون</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        .setup-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
        }
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .setup-header p {
            color: #666;
            font-size: 16px;
        }
        .setup-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .requirements {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .requirements h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .requirements ul {
            list-style-type: none;
            padding: 0;
        }
        .requirements li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .requirements li:last-child {
            border-bottom: none;
        }
        .check {
            color: #28a745;
            margin-left: 10px;
        }
        .cross {
            color: #dc3545;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>🏪 نظام إدارة المخزون والمبيعات</h1>
            <p>مرحباً بك في معالج تهيئة النظام</p>
        </div>

        <?php
        // التحقق من المتطلبات
        $requirements = [
            'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
            'PDO Extension' => extension_loaded('pdo'),
            'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
            'JSON Extension' => extension_loaded('json'),
            'MBString Extension' => extension_loaded('mbstring')
        ];

        $allRequirementsMet = true;
        foreach ($requirements as $requirement => $met) {
            if (!$met) {
                $allRequirementsMet = false;
                break;
            }
        }
        ?>

        <div class="requirements">
            <h3>متطلبات النظام</h3>
            <ul>
                <?php foreach ($requirements as $requirement => $met): ?>
                    <li>
                        <?php if ($met): ?>
                            <span class="check">✓</span>
                        <?php else: ?>
                            <span class="cross">✗</span>
                        <?php endif; ?>
                        <?php echo $requirement; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <?php if (!$allRequirementsMet): ?>
            <div class="error">
                <strong>تحذير:</strong> بعض المتطلبات غير متوفرة. يرجى التأكد من تثبيت جميع المتطلبات قبل المتابعة.
            </div>
        <?php else: ?>
            <div class="setup-content">
                <?php
                if (isset($_POST['setup'])) {
                    echo "<h3>جاري تهيئة قاعدة البيانات...</h3>";
                    
                    try {
                        require_once 'config/database.php';
                        
                        $database = new Database();
                        $result = $database->createDatabase();
                        
                        if ($result) {
                            echo '<div class="success">';
                            echo '<h4>تم إنشاء قاعدة البيانات بنجاح! 🎉</h4>';
                            echo '<p>تم إنشاء جميع الجداول وإدراج البيانات الأولية.</p>';
                            echo '<p><strong>بيانات تسجيل الدخول الافتراضية:</strong></p>';
                            echo '<p>اسم المستخدم: <code>admin</code></p>';
                            echo '<p>كلمة المرور: <code>admin123</code></p>';
                            echo '</div>';
                            
                            echo '<a href="index.html" class="btn">الانتقال إلى النظام</a>';
                        } else {
                            echo '<div class="error">حدث خطأ أثناء تهيئة قاعدة البيانات.</div>';
                        }
                    } catch (Exception $e) {
                        echo '<div class="error">خطأ: ' . $e->getMessage() . '</div>';
                    }
                } else {
                    ?>
                    <h3>إعدادات قاعدة البيانات</h3>
                    <p>سيتم إنشاء قاعدة البيانات والجداول المطلوبة تلقائياً.</p>
                    <p><strong>الإعدادات الافتراضية:</strong></p>
                    <ul>
                        <li>اسم قاعدة البيانات: inventory_system</li>
                        <li>المضيف: localhost</li>
                        <li>اسم المستخدم: root</li>
                        <li>كلمة المرور: (فارغة)</li>
                    </ul>
                    <p><small>يمكنك تعديل هذه الإعدادات في ملف config/database.php</small></p>
                    
                    <form method="post">
                        <button type="submit" name="setup" class="btn">بدء التهيئة</button>
                    </form>
                    <?php
                }
                ?>
            </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px; color: #666; font-size: 14px;">
            <p>نظام إدارة المخزون والمبيعات v1.0</p>
            <p>تم التطوير بواسطة فريق التطوير</p>
        </div>
    </div>
</body>
</html>