:root {
  --bg: linear-gradient(135deg, #f0f7ff, #fefefe);
  --card-bg: rgba(255, 255, 255, 0.75);
  --primary: #2563eb; /* أزرق أنيق */
  --accent: #f59e0b; /* برتقالي ذهبي */
  --muted: #6b7280;
  --success: #10b981;
  --danger: #ef4444;
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

body {
  font-family: 'Ta<PERSON>wal', sans-serif;
  margin: 0;
  background: var(--bg);
  backdrop-filter: blur(4px);
}

.card {
  background: var(--card-bg);
  backdrop-filter: blur(12px);
  border-radius: 20px;
  box-shadow: var(--shadow);
  padding: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}

.btn {
  background: var(--primary);
  color: #fff;
  border-radius: 12px;
  padding: 10px 14px;
  font-weight: 600;
  transition: background 0.2s ease;
}
.btn:hover {
  background: #1e40af;
}

.sidebar {
  background: linear-gradient(180deg, #2563eb, #1d4ed8);
  border-radius: 20px;
}

nav a {
  border-radius: 12px;
  padding: 10px;
  transition: background 0.2s;
}
nav a:hover {
  background: rgba(255,255,255,0.15);
}

.status {
  border-radius: 999px;
  padding: 6px 10px;
}
.status.paid {
  background: rgba(16,185,129,0.15);
  color: var(--success);
}
.status.due {
  background: rgba(245,158,11,0.15);
  color: var(--accent);
}
.status.over {
  background: rgba(239,68,68,0.15);
  color: var(--danger);
}
