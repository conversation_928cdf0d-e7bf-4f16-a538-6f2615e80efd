<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class PurchasesAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function getPurchaseInvoices() {
        try {
            $sql = "SELECT pi.*, s.name as supplier_name 
                    FROM purchase_invoices pi 
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id 
                    ORDER BY pi.created_at DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $totalPurchases = 0;
            $todayPurchases = 0;
            $todayInvoices = 0;

            foreach ($invoices as $invoice) {
                $totalPurchases += $invoice['total_amount'];
                
                if (date('Y-m-d', strtotime($invoice['created_at'])) == date('Y-m-d')) {
                    $todayPurchases += $invoice['total_amount'];
                    $todayInvoices++;
                }
            }

            return [
                'success' => true,
                'data' => $invoices,
                'summary' => [
                    'total_invoices' => count($invoices),
                    'total_purchases' => $totalPurchases,
                    'today_purchases' => $todayPurchases,
                    'today_invoices' => $todayInvoices
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
            ];
        }
    }

    public function createPurchaseInvoice($data) {
        try {
            $this->conn->beginTransaction();

            // إنشاء الفاتورة
            $sql = "INSERT INTO purchase_invoices (supplier_id, invoice_number, invoice_date, subtotal, tax_amount, discount_amount, total_amount, payment_method, notes, created_at) 
                    VALUES (:supplier_id, :invoice_number, :invoice_date, :subtotal, :tax_amount, :discount_amount, :total_amount, :payment_method, :notes, NOW())";
            
            $stmt = $this->conn->prepare($sql);
            $invoiceNumber = 'PUR-' . date('Ymd') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            
            $stmt->bindParam(':supplier_id', $data['supplier_id']);
            $stmt->bindParam(':invoice_number', $invoiceNumber);
            $stmt->bindParam(':invoice_date', date('Y-m-d'));
            $stmt->bindParam(':subtotal', $data['total_amount']);
            $stmt->bindParam(':tax_amount', $data['tax_amount']);
            $stmt->bindParam(':discount_amount', $data['discount']);
            $stmt->bindParam(':total_amount', $data['final_amount']);
            $stmt->bindParam(':payment_method', $data['payment_method']);
            $stmt->bindParam(':notes', $data['notes']);
            
            $stmt->execute();
            $invoiceId = $this->conn->lastInsertId();

            // إضافة عناصر الفاتورة
            foreach ($data['items'] as $item) {
                $sql = "INSERT INTO purchase_invoice_items (invoice_id, product_id, quantity, unit_price, total_price) 
                        VALUES (:invoice_id, :product_id, :quantity, :unit_price, :total_price)";
                
                $stmt = $this->conn->prepare($sql);
                $stmt->bindParam(':invoice_id', $invoiceId);
                $stmt->bindParam(':product_id', $item['product_id']);
                $stmt->bindParam(':quantity', $item['quantity']);
                $stmt->bindParam(':unit_price', $item['unit_price']);
                $stmt->bindParam(':total_price', $item['total_price']);
                $stmt->execute();

                // تحديث المخزون
                $sql = "UPDATE products SET quantity = quantity + :quantity WHERE id = :product_id";
                $stmt = $this->conn->prepare($sql);
                $stmt->bindParam(':quantity', $item['quantity']);
                $stmt->bindParam(':product_id', $item['product_id']);
                $stmt->execute();

                // تحديث تكلفة المنتج إذا تم تحديدها
                if (isset($item['new_cost_price']) && $item['new_cost_price'] > 0) {
                    $sql = "UPDATE products SET cost_price = :cost_price WHERE id = :product_id";
                    $stmt = $this->conn->prepare($sql);
                    $stmt->bindParam(':cost_price', $item['new_cost_price']);
                    $stmt->bindParam(':product_id', $item['product_id']);
                    $stmt->execute();
                }
            }

            // تحديث رصيد المورد إذا كان الدفع آجل
            if ($data['payment_method'] === 'credit') {
                $sql = "UPDATE suppliers SET balance = balance - :amount WHERE id = :supplier_id";
                $stmt = $this->conn->prepare($sql);
                $stmt->bindParam(':amount', $data['final_amount']);
                $stmt->bindParam(':supplier_id', $data['supplier_id']);
                $stmt->execute();
            }

            $this->conn->commit();

            return [
                'success' => true,
                'message' => 'تم إنشاء فاتورة المشتريات بنجاح',
                'invoice_id' => $invoiceId,
                'invoice_number' => $invoiceNumber
            ];
        } catch (PDOException $e) {
            $this->conn->rollBack();
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء الفاتورة: ' . $e->getMessage()
            ];
        }
    }

    public function getInvoiceDetails($invoiceId) {
        try {
            // جلب بيانات الفاتورة
            $sql = "SELECT pi.*, s.name as supplier_name, s.phone as supplier_phone 
                    FROM purchase_invoices pi 
                    LEFT JOIN suppliers s ON pi.supplier_id = s.id 
                    WHERE pi.id = :invoice_id";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoiceId);
            $stmt->execute();
            $invoice = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$invoice) {
                return [
                    'success' => false,
                    'message' => 'الفاتورة غير موجودة'
                ];
            }

            // جلب عناصر الفاتورة
            $sql = "SELECT pii.*, p.name as product_name, p.code as product_code 
                    FROM purchase_invoice_items pii 
                    LEFT JOIN products p ON pii.product_id = p.id 
                    WHERE pii.invoice_id = :invoice_id";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':invoice_id', $invoiceId);
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $invoice['items'] = $items;

            return [
                'success' => true,
                'data' => $invoice
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب تفاصيل الفاتورة: ' . $e->getMessage()
            ];
        }
    }

    public function getPurchasesReport($startDate = null, $endDate = null) {
        try {
            $whereClause = "";
            if ($startDate && $endDate) {
                $whereClause = "WHERE DATE(pi.created_at) BETWEEN :start_date AND :end_date";
            }

            $sql = "SELECT 
                        COUNT(*) as total_invoices,
                        SUM(pi.final_amount) as total_purchases,
                        AVG(pi.final_amount) as average_invoice,
                        SUM(pi.discount) as total_discount,
                        SUM(pi.tax_amount) as total_tax
                    FROM purchase_invoices pi $whereClause";
            
            $stmt = $this->conn->prepare($sql);
            if ($startDate && $endDate) {
                $stmt->bindParam(':start_date', $startDate);
                $stmt->bindParam(':end_date', $endDate);
            }
            $stmt->execute();
            $summary = $stmt->fetch(PDO::FETCH_ASSOC);

            // أكثر المنتجات شراءً
            $sql = "SELECT 
                        p.name as product_name,
                        p.code as product_code,
                        SUM(pii.quantity) as total_quantity,
                        SUM(pii.total_price) as total_purchases
                    FROM purchase_invoice_items pii
                    LEFT JOIN products p ON pii.product_id = p.id
                    LEFT JOIN purchase_invoices pi ON pii.invoice_id = pi.id
                    $whereClause
                    GROUP BY pii.product_id
                    ORDER BY total_quantity DESC
                    LIMIT 10";
            
            $stmt = $this->conn->prepare($sql);
            if ($startDate && $endDate) {
                $stmt->bindParam(':start_date', $startDate);
                $stmt->bindParam(':end_date', $endDate);
            }
            $stmt->execute();
            $topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => [
                    'summary' => $summary,
                    'top_products' => $topProducts
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء التقرير: ' . $e->getMessage()
            ];
        }
    }
}

// معالجة الطلبات
$api = new PurchasesAPI();
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['invoice_id'])) {
            echo json_encode($api->getInvoiceDetails($_GET['invoice_id']));
        } elseif (isset($_GET['report'])) {
            $startDate = $_GET['start_date'] ?? null;
            $endDate = $_GET['end_date'] ?? null;
            echo json_encode($api->getPurchasesReport($startDate, $endDate));
        } else {
            echo json_encode($api->getPurchaseInvoices());
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        echo json_encode($api->createPurchaseInvoice($input));
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}
?>