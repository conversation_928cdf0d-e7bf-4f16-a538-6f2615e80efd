<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class CategoryAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    // جلب جميع الفئات
    public function getCategories() {
        try {
            $sql = "SELECT c.*, COUNT(p.id) as products_count 
                    FROM categories c 
                    LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                    WHERE c.is_active = 1 
                    GROUP BY c.id
                    ORDER BY c.created_at DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => $categories,
                'count' => count($categories)
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب الفئات: ' . $e->getMessage()
            ];
        }
    }

    // جلب فئة واحدة
    public function getCategory($id) {
        try {
            $sql = "SELECT c.*, COUNT(p.id) as products_count 
                    FROM categories c 
                    LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                    WHERE c.id = ? AND c.is_active = 1
                    GROUP BY c.id";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$id]);
            $category = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($category) {
                return [
                    'success' => true,
                    'data' => $category
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'الفئة غير موجودة'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب الفئة: ' . $e->getMessage()
            ];
        }
    }

    // إضافة فئة جديدة
    public function addCategory($data) {
        try {
            if (empty($data['name'])) {
                return [
                    'success' => false,
                    'message' => 'اسم الفئة مطلوب'
                ];
            }

            // التحقق من عدم تكرار الاسم
            $checkSql = "SELECT id FROM categories WHERE name = ? AND is_active = 1";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$data['name']]);
            
            if ($checkStmt->fetch()) {
                return [
                    'success' => false,
                    'message' => 'اسم الفئة موجود مسبقاً'
                ];
            }

            $sql = "INSERT INTO categories (name, description) VALUES (?, ?)";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $data['name'],
                $data['description'] ?? ''
            ]);

            if ($result) {
                return [
                    'success' => true,
                    'message' => 'تم إضافة الفئة بنجاح',
                    'category_id' => $this->conn->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في إضافة الفئة'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إضافة الفئة: ' . $e->getMessage()
            ];
        }
    }

    // تحديث فئة
    public function updateCategory($id, $data) {
        try {
            $checkSql = "SELECT id FROM categories WHERE id = ? AND is_active = 1";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$id]);
            
            if (!$checkStmt->fetch()) {
                return [
                    'success' => false,
                    'message' => 'الفئة غير موجودة'
                ];
            }

            $sql = "UPDATE categories SET name = ?, description = ? WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([
                $data['name'],
                $data['description'] ?? '',
                $id
            ]);

            if ($result) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث الفئة بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في تحديث الفئة'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في تحديث الفئة: ' . $e->getMessage()
            ];
        }
    }

    // حذف فئة
    public function deleteCategory($id) {
        try {
            // التحقق من عدم وجود منتجات في هذه الفئة
            $checkSql = "SELECT COUNT(*) as count FROM products WHERE category_id = ? AND is_active = 1";
            $checkStmt = $this->conn->prepare($checkSql);
            $checkStmt->execute([$id]);
            $result = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($result['count'] > 0) {
                return [
                    'success' => false,
                    'message' => 'لا يمكن حذف الفئة لوجود منتجات مرتبطة بها'
                ];
            }

            $sql = "UPDATE categories SET is_active = 0 WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $result = $stmt->execute([$id]);

            if ($result && $stmt->rowCount() > 0) {
                return [
                    'success' => true,
                    'message' => 'تم حذف الفئة بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'الفئة غير موجودة أو تم حذفها مسبقاً'
                ];
            }
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في حذف الفئة: ' . $e->getMessage()
            ];
        }
    }
}

// معالجة الطلبات
$categoryAPI = new CategoryAPI();
$method = $_SERVER['REQUEST_METHOD'];
$response = [];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            $response = $categoryAPI->getCategory($_GET['id']);
        } else {
            $response = $categoryAPI->getCategories();
        }
        break;
        
    case 'POST':
        $data = json_decode(file_get_contents('php://input'), true);
        $response = $categoryAPI->addCategory($data);
        break;
        
    case 'PUT':
        if (isset($_GET['id'])) {
            $data = json_decode(file_get_contents('php://input'), true);
            $response = $categoryAPI->updateCategory($_GET['id'], $data);
        } else {
            $response = ['success' => false, 'message' => 'معرف الفئة مطلوب'];
        }
        break;
        
    case 'DELETE':
        if (isset($_GET['id'])) {
            $response = $categoryAPI->deleteCategory($_GET['id']);
        } else {
            $response = ['success' => false, 'message' => 'معرف الفئة مطلوب'];
        }
        break;
        
    default:
        $response = ['success' => false, 'message' => 'طريقة غير مدعومة'];
        break;
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>