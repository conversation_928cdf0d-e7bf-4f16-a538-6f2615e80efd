<?php
// تسجيل جميع الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// إعداد الرؤوس
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // التحقق من وجود ملف قاعدة البيانات
    if (!file_exists('../config/database.php')) {
        throw new Exception('ملف قاعدة البيانات غير موجود');
    }
    
    require_once '../config/database.php';
    
    // التحقق من وجود الكلاس
    if (!class_exists('Database')) {
        throw new Exception('كلاس قاعدة البيانات غير موجود');
    }
    
    class ProductAPI {
        private $db;
        private $conn;

        public function __construct() {
            $this->db = new Database();
            $this->conn = $this->db->connect();
        }

        public function getProducts() {
            try {
                $sql = "SELECT p.*, c.name as category_name 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.is_active = 1 
                        ORDER BY p.created_at DESC";

                $stmt = $this->conn->prepare($sql);
                $stmt->execute();
                $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                $totalValue = 0;
                $lowStockCount = 0;

                foreach ($products as &$product) {
                    $product['total_value'] = $product['quantity'] * $product['selling_price'];
                    $totalValue += $product['total_value'];

                    $product['low_stock'] = $product['quantity'] <= $product['min_stock'];
                }

                return [
                    'success' => true,
                    'data' => $products,
                    'summary' => [
                        'total_products' => count($products),
                        'total_value' => $totalValue,
                        'low_stock_count' => $lowStockCount
                    ]
                ];
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في جلب المنتجات: ' . $e->getMessage()
                ];
            }
        }

        public function getProduct($id) {
            try {
                $sql = "SELECT p.*, c.name as category_name 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.id = ? AND p.is_active = 1";

                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$id]);
                $product = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($product) {
                    $product['total_value'] = $product['quantity'] * $product['selling_price'];
                    $product['low_stock'] = $product['quantity'] <= $product['min_stock'];

                    return [
                        'success' => true,
                        'data' => $product
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'المنتج غير موجود'
                    ];
                }
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في جلب المنتج: ' . $e->getMessage()
                ];
            }
        }

        public function addProduct($data) {
            try {
                // التحقق من البيانات المطلوبة
                if (empty($data['name'])) {
                    return [
                        'success' => false,
                        'message' => 'اسم المنتج مطلوب'
                    ];
                }

                if (empty($data['code'])) {
                    return [
                        'success' => false,
                        'message' => 'كود المنتج مطلوب'
                    ];
                }

                // التحقق من عدم تكرار الكود
                $checkSql = "SELECT id FROM products WHERE code = ?";
                $checkStmt = $this->conn->prepare($checkSql);
                $checkStmt->execute([$data['code']]);

                if ($checkStmt->fetch()) {
                    return [
                        'success' => false,
                        'message' => 'كود المنتج موجود مسبقًا'
                    ];
                }

                // توليد باركود تلقائي إذا لم يرسل
                if (empty($data['barcode'])) {
                    $data['barcode'] = str_pad(rand(1000000000000, 9999999999999), 13, '0', STR_PAD_LEFT);
                }

                $sql = "INSERT INTO products (code, name, description, category_id, quantity, min_stock, cost_price, selling_price, barcode, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                $stmt = $this->conn->prepare($sql);
                $result = $stmt->execute([
                    $data['code'],
                    $data['name'],
                    $data['description'] ?? '',
                    !empty($data['category_id']) ? $data['category_id'] : null,
                    $data['quantity'] ?? 0,
                    $data['min_stock'] ?? 0,
                    $data['cost_price'] ?? 0,
                    $data['selling_price'] ?? 0,
                    $data['barcode']
                ]);

                if ($result) {
                    return [
                        'success' => true,
                        'message' => 'تم إضافة المنتج بنجاح',
                        'product_id' => $this->conn->lastInsertId(),
                        'barcode' => $data['barcode']
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'فشل في إضافة المنتج'
                    ];
                }
            } catch (PDOException $e) {
                error_log("خطأ في addProduct: " . $e->getMessage());
                return [
                    'success' => false,
                    'message' => 'خطأ في قاعدة البيانات'
                ];
            }
        }

        public function updateProduct($id, $data) {
            try {
                $checkSql = "SELECT id FROM products WHERE id = ? AND is_active = 1";
                $checkStmt = $this->conn->prepare($checkSql);
                $checkStmt->execute([$id]);

                if (!$checkStmt->fetch()) {
                    return [
                        'success' => false,
                        'message' => 'المنتج غير موجود'
                    ];
                }

                $sql = "UPDATE products SET 
                        name = ?, description = ?, category_id = ?, quantity = ?, 
                        min_stock = ?, cost_price = ?, selling_price = ?, barcode = ?,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?";

                $stmt = $this->conn->prepare($sql);
                $result = $stmt->execute([
                    $data['name'],
                    $data['description'] ?? '',
                    $data['category_id'] ?? null,
                    $data['quantity'] ?? 0,
                    $data['min_stock'] ?? 0,
                    $data['cost_price'] ?? 0,
                    $data['selling_price'] ?? 0,
                    $data['barcode'] ?? '',
                    $id
                ]);

                if ($result) {
                    return [
                        'success' => true,
                        'message' => 'تم تحديث المنتج بنجاح'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'فشل في تحديث المنتج'
                    ];
                }
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في تحديث المنتج: ' . $e->getMessage()
                ];
            }
        }

        public function deleteProduct($id) {
            try {
                $sql = "UPDATE products SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
                $stmt = $this->conn->prepare($sql);
                $result = $stmt->execute([$id]);

                if ($result && $stmt->rowCount() > 0) {
                    return [
                        'success' => true,
                        'message' => 'تم حذف المنتج بنجاح'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'المنتج غير موجود أو تم حذفه مسبقًا'
                    ];
                }
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في حذف المنتج: ' . $e->getMessage()
                ];
            }
        }

        public function searchProducts($query) {
            try {
                $sql = "SELECT p.*, c.name as category_name 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.is_active = 1 AND (
                            p.name LIKE ? OR 
                            p.code LIKE ? OR 
                            p.description LIKE ? OR
                            p.barcode LIKE ? OR
                            c.name LIKE ?
                        )
                        ORDER BY p.name";

                $searchTerm = "%$query%";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
                $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($products as &$product) {
                    $product['total_value'] = $product['quantity'] * $product['selling_price'];
                    $product['low_stock'] = $product['quantity'] <= $product['min_stock'];
                }

                return [
                    'success' => true,
                    'data' => $products,
                    'count' => count($products)
                ];
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في البحث: ' . $e->getMessage()
                ];
            }
        }

        public function getLowStockProducts() {
            try {
                $sql = "SELECT p.*, c.name as category_name 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.is_active = 1 AND p.quantity <= p.min_stock
                        ORDER BY p.quantity ASC";

                $stmt = $this->conn->prepare($sql);
                $stmt->execute();
                $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                return [
                    'success' => true,
                    'data' => $products,
                    'count' => count($products)
                ];
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في جلب المنتجات منخفضة المخزون: ' . $e->getMessage()
                ];
            }
        }

        public function searchByBarcode($barcode) {
            try {
                $sql = "SELECT p.*, c.name as category_name 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.is_active = 1 AND p.barcode = ?
                        ORDER BY p.name";

                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$barcode]);
                $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($products as &$product) {
                    $product['total_value'] = $product['quantity'] * $product['selling_price'];
                    $product['low_stock'] = $product['quantity'] <= $product['min_stock'];
                }

                return [
                    'success' => true,
                    'data' => $products,
                    'count' => count($products)
                ];
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في البحث بالكود بار: ' . $e->getMessage()
                ];
            }
        }

        // إضافة وظيفة توليد كود بار EAN-13
        private function generateEAN13() {
            // توليد 12 رقم عشوائي
            $code = '';
            for ($i = 0; $i < 12; $i++) {
                $code .= rand(0, 9);
            }
            
            // حساب رقم التحقق
            $checksum = $this->calculateEAN13Checksum($code);
            return $code . $checksum;
        }
        
        private function calculateEAN13Checksum($code) {
            $sum = 0;
            for ($i = 0; $i < 12; $i++) {
                $digit = (int)$code[$i];
                $sum += ($i % 2 === 0) ? $digit : $digit * 3;
            }
            return (10 - ($sum % 10)) % 10;
        }
        
        // البحث المتقدم بالكود بار مع تفاصيل إضافية
        public function searchByBarcodeAdvanced($barcode) {
            try {
                $sql = "SELECT p.*, c.name as category_name,
                        (SELECT COUNT(*) FROM sales_invoice_items sii 
                         JOIN sales_invoices si ON sii.invoice_id = si.id 
                         WHERE sii.product_id = p.id AND si.is_active = 1) as sales_count,
                        (SELECT SUM(sii.quantity) FROM sales_invoice_items sii 
                         JOIN sales_invoices si ON sii.invoice_id = si.id 
                         WHERE sii.product_id = p.id AND si.is_active = 1) as total_sold
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.is_active = 1 AND p.barcode = ?";

                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$barcode]);
                $product = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($product) {
                    $product['total_value'] = $product['quantity'] * $product['selling_price'];
                    $product['low_stock'] = $product['quantity'] <= $product['min_stock'];
                    $product['profit_margin'] = $product['selling_price'] > 0 ? 
                        (($product['selling_price'] - $product['cost_price']) / $product['selling_price']) * 100 : 0;
                    
                    return [
                        'success' => true,
                        'data' => $product
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'لم يتم العثور على منتج بهذا الكود بار'
                    ];
                }
            } catch (PDOException $e) {
                return [
                    'success' => false,
                    'message' => 'خطأ في البحث: ' . $e->getMessage()
                ];
            }
        }
    }

    $productAPI = new ProductAPI();
    $method = $_SERVER['REQUEST_METHOD'];
    $response = [];

    // تسجيل الطلب
    error_log("طلب API: $method " . $_SERVER['REQUEST_URI']);

    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                $response = $productAPI->getProduct($_GET['id']);
            } elseif (isset($_GET['search'])) {
                $response = $productAPI->searchProducts($_GET['search']);
            } elseif (isset($_GET['barcode'])) {
                $response = $productAPI->searchByBarcode($_GET['barcode']);
            } else {
                $response = $productAPI->getProducts();
            }
            break;

        case 'POST':
            // قراءة البيانات
            $input = file_get_contents('php://input');
            error_log("البيانات المستلمة: " . $input);
            
            if (empty($input)) {
                $response = [
                    'success' => false,
                    'message' => 'لم يتم إرسال بيانات'
                ];
                break;
            }
            
            $data = json_decode($input, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $response = [
                    'success' => false,
                    'message' => 'خطأ في تحليل البيانات: ' . json_last_error_msg()
                ];
                break;
            }
            
            error_log("البيانات المحللة: " . print_r($data, true));
            
            // توليد كود إذا لم يرسل
            if (empty($data['code'])) {
                $data['code'] = 'P' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            }
            
            $response = $productAPI->addProduct($data);
            break;
            
        default:
            $response = [
                'success' => false, 
                'message' => 'طريقة غير مدعومة: ' . $method
            ];
            break;
    }
    
} catch (Exception $e) {
    error_log("خطأ في products.php: " . $e->getMessage());
    $response = [
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ];
}

// تسجيل الاستجابة
error_log("الاستجابة: " . json_encode($response, JSON_UNESCAPED_UNICODE));

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
