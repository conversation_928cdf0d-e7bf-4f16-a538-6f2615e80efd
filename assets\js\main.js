// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    setupNavigation();
    updateDateTime();
    initializeCharts();
    
    // تحديث الوقت كل ثانية
    setInterval(updateDateTime, 1000);
}

// إعداد التنقل بين الصفحات
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const pages = document.querySelectorAll('.page');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetPage = this.getAttribute('data-page');
            
            // إزالة الفئة النشطة من جميع الروابط والصفحات
            navLinks.forEach(l => l.parentElement.classList.remove('active'));
            pages.forEach(p => p.classList.remove('active'));
            
            // إضافة الفئة النشطة للرابط والصفحة المحددة
            this.parentElement.classList.add('active');
            document.getElementById(targetPage).classList.add('active');
            
            // تحميل محتوى الصفحة إذا لزم الأمر
            loadPageContent(targetPage);
        });
    });
}

// تحديث التاريخ والوقت
function updateDateTime() {
    const now = new Date();
    const dateElement = document.getElementById('current-date');
    const timeElement = document.getElementById('current-time');
    
    if (dateElement && timeElement) {
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        
        dateElement.textContent = now.toLocaleDateString('ar-SA', options);
        timeElement.textContent = now.toLocaleTimeString('ar-SA');
    }
}

// تهيئة الرسوم البيانية
function initializeCharts() {
    const ctx = document.getElementById('salesChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                datasets: [{
                    label: 'المبيعات (ريال)',
                    data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }
}

// تحميل محتوى الصفحة
function loadPageContent(pageName) {
    switch(pageName) {
        case 'inventory':
            loadInventoryPage();
            break;
        case 'customers':
            loadCustomersPage();
            break;
        case 'suppliers':
            loadSuppliersPage();
            break;
        case 'sales':
            loadSalesPage();
            break;
        case 'purchases':
            loadPurchasesPage();
            break;
        case 'reports':
            loadReportsPage();
            break;
        case 'invoices':
            loadInvoicesPage();
            break;
        case 'users':
            loadUsersPage();
            break;
        case 'settings':
            loadSettingsPage();
            break;
        case 'support':
            loadSupportPage();
            break;
    }
}

// تحميل صفحة المخزون
function loadInventoryPage() {
    const inventoryPage = document.getElementById('inventory');
    if (inventoryPage.children.length <= 1) {
        // تحميل البيانات من API
        loadInventoryData();
        
        inventoryPage.innerHTML += `
            <div class="inventory-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>إجمالي قيمة المخزون</h4>
                        <p class="summary-value">245,680 ريال</p>
                    </div>
                    <div class="summary-card">
                        <h4>عدد المنتجات</h4>
                        <p class="summary-value">1,245</p>
                    </div>
                    <div class="summary-card">
                        <h4>منتجات منخفضة المخزون</h4>
                        <p class="summary-value warning">15</p>
                    </div>
                </div>
            </div>
            
            <div class="search-filter-bar">
                <div class="search-box">
                    <input type="text" placeholder="البحث عن منتج..." class="form-control" id="product-search">
                    <i class="fas fa-search"></i>
                </div>
                <div class="barcode-search-box">
                    <input type="text" placeholder="البحث بالكود بار..." class="form-control" id="barcode-search">
                    <button class="btn btn-info" onclick="scanBarcodeForSearch()">
                        <i class="fas fa-camera"></i> مسح الكود بار
                    </button>
                </div>
                <select class="form-control" style="width: auto;">
                    <option>جميع الفئات</option>
                    <option>إلكترونيات</option>
                    <option>ملابس</option>
                    <option>أدوات منزلية</option>
                </select>
                <button class="btn btn-primary" onclick="openAddProductModal()">
                    <i class="fas fa-plus"></i> إضافة منتج
                </button>
            </div>
            
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم المنتج</th>
                            <th>الكود بار</th>
                            <th>الفئة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>التكلفة</th>
                            <th>الرصيد الكلي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="inventory-table-body">
                        <tr data-product-id="P001">
                            <td>P001</td>
                            <td>لابتوب ديل</td>
                            <td>
                                <div class="barcode-cell">
                                    <span class="barcode-text">1234567890123</span>
                                    <button class="btn btn-xs btn-secondary" onclick="generateBarcode('P001', '1234567890123')">
                                        <i class="fas fa-barcode"></i>
                                    </button>
                                </div>
                            </td>
                            <td>إلكترونيات</td>
                            <td class="low-stock">5</td>
                            <td>2,500 ريال</td>
                            <td>2,000 ريال</td>
                            <td>12,500 ريال</td>
                            <td><span class="status-badge warning">مخزون منخفض</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editProduct('P001')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteProduct('P001')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        <tr data-product-id="P002">
                            <td>P002</td>
                            <td>هاتف سامسونج</td>
                            <td>
                                <div class="barcode-cell">
                                    <span class="barcode-text">9876543210987</span>
                                    <button class="btn btn-xs btn-secondary" onclick="generateBarcode('P002', '9876543210987')">
                                        <i class="fas fa-barcode"></i>
                                    </button>
                                </div>
                            </td>
                            <td>إلكترونيات</td>
                            <td>25</td>
                            <td>1,200 ريال</td>
                            <td>900 ريال</td>
                            <td>30,000 ريال</td>
                            <td><span class="status-badge success">متوفر</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editProduct('P002')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteProduct('P002')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
}

// تحميل صفحة العملاء
function loadCustomersPage() {
    const customersPage = document.getElementById('customers');
    if (customersPage.children.length <= 1) {
        // تحميل البيانات من API
        loadCustomersData();
        customersPage.innerHTML += `
            <div class="customers-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>إجمالي العملاء</h4>
                        <p class="summary-value">156</p>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي الديون</h4>
                        <p class="summary-value">45,680 ريال</p>
                    </div>
                    <div class="summary-card">
                        <h4>عملاء جدد هذا الشهر</h4>
                        <p class="summary-value">12</p>
                    </div>
                </div>
            </div>
            
            <div class="search-filter-bar">
                <div class="search-box">
                    <input type="text" placeholder="البحث عن عميل..." class="form-control" id="customer-search">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم العميل</th>
                            <th>المدينة</th>
                            <th>رقم الهاتف</th>
                            <th>مبلغ الدين</th>
                            <th>تاريخ آخر عملية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>C001</td>
                            <td>أحمد محمد</td>
                            <td>الرياض</td>
                            <td>0501234567</td>
                            <td class="debt-amount">2,500 ريال</td>
                            <td>2024-01-15</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editCustomer('C001')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewCustomer('C001')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="printCustomerCard('C001')">
                                    <i class="fas fa-print"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>C002</td>
                            <td>فاطمة علي</td>
                            <td>جدة</td>
                            <td>0507654321</td>
                            <td class="debt-amount">1,200 ريال</td>
                            <td>2024-01-20</td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick="editCustomer('C002')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewCustomer('C002')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-success" onclick="printCustomerCard('C002')">
                                    <i class="fas fa-print"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }
}

// فتح نافذة إضافة منتج
function openAddProductModal() {
    showModal('إضافة منتج جديد', `
        <form id="add-product-form">
            <div class="form-group">
                <label class="form-label">اسم المنتج *</label>
                <input type="text" class="form-control" name="product_name" required>
            </div>
            <div class="form-group">
                <label class="form-label">كود المنتج</label>
                <input type="text" class="form-control" name="code" placeholder="سيتم توليده تلقائين">
            </div>
            <div class="form-group">
                <label class="form-label">الوصف</label>
                <textarea class="form-control" name="description" rows="2"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">الفئة</label>
                <select class="form-control" name="category">
                    <option value="">اختر الفئة</option>
                    <option value="1">إلكترونيات</option>
                    <option value="2">ملابس</option>
                    <option value="3">أدوات منزلية</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">الكمية</label>
                <input type="number" class="form-control" name="quantity" value="0" min="0">
            </div>
            <div class="form-group">
                <label class="form-label">سعر التكلفة</label>
                <input type="number" class="form-control" name="cost" step="0.01" value="0" min="0">
            </div>
            <div class="form-group">
                <label class="form-label">سعر البيع</label>
                <input type="number" class="form-control" name="price" step="0.01" value="0" min="0">
            </div>
            <div class="form-group">
                <label class="form-label">الحد الأدنى للمخزون</label>
                <input type="number" class="form-control" name="min_stock" value="0" min="0">
            </div>
            <div class="form-group">
                <label class="form-label">الكود بار</label>
                <input type="text" class="form-control" name="barcode" placeholder="سيتم توليده تلقائين">
            </div>
            <div class="modal-actions">
                <button type="submit" class="btn btn-primary">حفظ المنتج</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            </div>
        </form>
    `);
    
    setupAddProductForm();
}

// تحسين معالج النموذج
function setupAddProductForm() {
    const form = document.getElementById('add-product-form');
    if (!form) {
        console.error('لم يتم العثور على النموذج');
        return;
    }
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        console.log('تم إرسال النموذج');
        
        // إظهار مؤشر التحميل
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'جاري الحفظ...';
        submitBtn.disabled = true;
        
        try {
            const formData = new FormData(this);
            
            // التحقق من البيانات المطلوبة
            const productName = formData.get('product_name');
            if (!productName || productName.trim() === '') {
                showAlert('اسم المنتج مطلوب', 'warning');
                return;
            }
            
            // إعداد البيانات
            const productData = {
                name: productName.trim(),
                code: formData.get('code') || 'P' + Date.now(),
                description: formData.get('description') || '',
                category_id: formData.get('category') || null,
                quantity: parseInt(formData.get('quantity')) || 0,
                selling_price: parseFloat(formData.get('price')) || 0,
                cost_price: parseFloat(formData.get('cost')) || 0,
                min_stock: parseInt(formData.get('min_stock')) || 0,
                barcode: formData.get('barcode') || ''
            };
            
            console.log('بيانات المنتج:', productData);
            
            // إرسال البيانات
            const success = await saveProduct(productData);
            if (success) {
                closeModal();
                this.reset();
                showAlert('تم إضافة المنتج بنجاح!', 'success');
            }
            
        } catch (error) {
            console.error('خطأ في معالجة النموذج:', error);
            showAlert('خطأ في معالجة البيانات: ' + error.message, 'danger');
        } finally {
            // إعادة تعيين الزر
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });
}

// فتح نافذة إضافة عميل
function openAddCustomerModal() {
    showModal('إضافة عميل جديد', `
        <form id="add-customer-form">
            <div class="form-group">
                <label class="form-label">اسم العميل</label>
                <input type="text" class="form-control" name="customer_name" required>
            </div>
            <div class="form-group">
                <label class="form-label">رقم الهاتف</label>
                <input type="tel" class="form-control" name="phone" required>
            </div>
            <div class="form-group">
                <label class="form-label">المدينة</label>
                <input type="text" class="form-control" name="city" required>
            </div>
            <div class="form-group">
                <label class="form-label">العنوان</label>
                <textarea class="form-control" name="address" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">الرصيد الافتتاحي</label>
                <input type="number" class="form-control" name="initial_balance" step="0.01" value="0">
            </div>
            <div class="modal-actions">
                <button type="submit" class="btn btn-primary">حفظ العميل</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            </div>
        </form>
    `);
    
    document.getElementById('add-customer-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const customerData = {
            name: formData.get('customer_name'),
            phone: formData.get('phone'),
            city: formData.get('city'),
            address: formData.get('address'),
            balance: parseFloat(formData.get('initial_balance')) || 0
        };
        
        const success = await saveCustomer(customerData);
        if (success) {
            closeModal();
        }
    });
}

// عرض النافذة المنبثقة
function showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// إغلاق النافذة المنبثقة
function closeModal() {
    const modal = document.querySelector('.modal');
    if (modal) {
        modal.remove();
    }
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info') {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.minWidth = '300px';
    
    document.body.appendChild(alert);
    
    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

// وظائف إضافية للمنتجات
function editProduct(productId) {
    // في الحالة الحقيقية، يجب جلب بيانات المنتج من الخادم أولاً
    // هنا نستخدم بيانات افتراضية للتوضيح
    
    // يمكن استخدام هذا الكود لجلب بيانات المنتج من الخادم
    // const product = await apiRequest(`api/products.php?id=${productId}`, 'GET');
    
    // بيانات افتراضية للعرض
    const productData = {
        id: productId,
        name: productId === 'P001' ? 'لابتوب ديل' : 'هاتف سامسونج',
        category: 'إلكترونيات',
        quantity: productId === 'P001' ? 5 : 25,
        price: productId === 'P001' ? 2500 : 1200,
        cost: productId === 'P001' ? 2000 : 900,
        min_stock: productId === 'P001' ? 10 : 15
    };
    
    showModal('تعديل المنتج', `
        <form id="edit-product-form">
            <input type="hidden" name="product_id" value="${productId}">
            <div class="form-group">
                <label class="form-label">اسم المنتج</label>
                <input type="text" class="form-control" name="product_name" value="${productData.name}" required>
            </div>
            <div class="form-group">
                <label class="form-label">الفئة</label>
                <select class="form-control" name="category" required>
                    <option value="electronics" ${productData.category === 'إلكترونيات' ? 'selected' : ''}>إلكترونيات</option>
                    <option value="clothing" ${productData.category === 'ملابس' ? 'selected' : ''}>ملابس</option>
                    <option value="home" ${productData.category === 'أدوات منزلية' ? 'selected' : ''}>أدوات منزلية</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">الكمية</label>
                <input type="number" class="form-control" name="quantity" value="${productData.quantity}" required>
            </div>
            <div class="form-group">
                <label class="form-label">السعر</label>
                <input type="number" class="form-control" name="price" step="0.01" value="${productData.price}" required>
            </div>
            <div class="form-group">
                <label class="form-label">التكلفة</label>
                <input type="number" class="form-control" name="cost" step="0.01" value="${productData.cost}" required>
            </div>
            <div class="form-group">
                <label class="form-label">الحد الأدنى للمخزون</label>
                <input type="number" class="form-control" name="min_stock" value="${productData.min_stock}" required>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-primary" onclick="saveProductChanges('${productId}')">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            </div>
        </form>
    `);
    
    // إضافة مستمع للنموذج
    document.getElementById('edit-product-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveProductChanges(productId);
    });
}

function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        showAlert('تم حذف المنتج: ' + productId, 'success');
    }
}

// حفظ تغييرات المنتج
function saveProductChanges(productId) {
    // جمع البيانات من النموذج
    const form = document.getElementById('edit-product-form');
    const productName = form.querySelector('[name="product_name"]').value;
    const category = form.querySelector('[name="category"]').value;
    const quantity = form.querySelector('[name="quantity"]').value;
    const price = form.querySelector('[name="price"]').value;
    const cost = form.querySelector('[name="cost"]').value;
    const minStock = form.querySelector('[name="min_stock"]').value;
    
    // التحقق من صحة البيانات
    if (!productName || !quantity || !price || !cost) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // في الحالة الحقيقية، يجب إرسال البيانات إلى الخادم
    // هنا نعرض فقط رسالة نجاح
    
    // يمكن استخدام هذا الكود لإرسال البيانات إلى الخادم
    /*
    apiRequest(`api/products.php?id=${productId}`, 'PUT', {
        name: productName,
        category_id: getCategoryId(category),
        quantity: parseInt(quantity),
        selling_price: parseFloat(price),
        cost_price: parseFloat(cost),
        min_stock: parseInt(minStock)
    }).then(response => {
        if (response.success) {
            showAlert('تم تحديث المنتج بنجاح', 'success');
            closeModal();
            // تحديث الجدول
            loadInventoryData();
        } else {
            showAlert('حدث خطأ أثناء تحديث المنتج: ' + response.message, 'error');
        }
    }).catch(error => {
        showAlert('حدث خطأ في الاتصال بالخادم', 'error');
    });
    */
    
    // عرض رسالة نجاح وإغلاق النافذة
    showAlert('تم تحديث المنتج بنجاح', 'success');
    closeModal();
    
    // تحديث الجدول (في الحالة الحقيقية، يجب إعادة تحميل البيانات)
    // هنا نقوم بتحديث الصف في الجدول مباشرة للتوضيح
    const tableRow = document.querySelector(`tr[data-product-id="${productId}"]`);
    if (tableRow) {
        const cells = tableRow.querySelectorAll('td');
        if (cells.length >= 7) {
            cells[1].textContent = productName;
            cells[3].textContent = quantity;
            cells[4].textContent = price + ' ريال';
            cells[5].textContent = cost + ' ريال';
            cells[6].textContent = (quantity * price) + ' ريال';
            
            // تحديث حالة المخزون
            if (parseInt(quantity) <= parseInt(minStock)) {
                cells[3].className = 'low-stock';
                cells[7].innerHTML = '<span class="status-badge warning">مخزون منخفض</span>';
            } else {
                cells[3].className = '';
                cells[7].innerHTML = '<span class="status-badge success">متوفر</span>';
            }
        }
    }
}

// وظائف إضافية للعملاء
function editCustomer(customerId) {
    showAlert('فتح نافذة تعديل العميل: ' + customerId, 'info');
}

function viewCustomer(customerId) {
    showAlert('عرض تفاصيل العميل: ' + customerId, 'info');
}

function printCustomerCard(customerId) {
    showAlert('طباعة بطاقة العميل: ' + customerId, 'info');
}

// وظائف API
async function apiRequest(url, method = 'GET', data = null) {
    try {
        console.log(`API Request: ${method} ${url}`, data); // للتشخيص
        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        
        console.log(`API Response Status: ${response.status}`); // للتشخيص
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Response is not JSON:', text);
            throw new Error('الاستجابة ليست بصيغة JSON صحيحة');
        }
        
        const result = await response.json();
        console.log('API Result:', result); // للتشخيص
        
        return result;
    } catch (error) {
        console.error('API Error:', error);
        return {
            success: false,
            message: 'خطأ في الاتصال بالخادم: ' + error.message
        };
    }
}

// تحميل بيانات المخزون
async function loadInventoryData() {
    try {
        const response = await apiRequest('api/products.php');
        if (response.success) {
            updateInventorySummary(response.summary);
            updateInventoryTable(response.data);
        } else {
            showAlert('خطأ في تحميل بيانات المخزون: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في تحميل بيانات المخزون', 'danger');
    }
}

// تحديث ملخص المخزون
function updateInventorySummary(summary) {
    const summaryContainer = document.querySelector('.inventory-summary .summary-cards');
    if (summaryContainer) {
        summaryContainer.innerHTML = `
            <div class="summary-card">
                <h4>إجمالي قيمة المخزون</h4>
                <p class="summary-value">${summary.total_value.toLocaleString()} ريال</p>
            </div>
            <div class="summary-card">
                <h4>عدد المنتجات</h4>
                <p class="summary-value">${summary.total_products}</p>
            </div>
            <div class="summary-card">
                <h4>منتجات منخفضة المخزون</h4>
                <p class="summary-value warning">${summary.low_stock_count}</p>
            </div>
        `;
    }
}

// تحديث جدول المخزون
function updateInventoryTable(products) {
    const tableBody = document.getElementById('inventory-table-body');
    if (tableBody) {
        tableBody.innerHTML = '';
        
        products.forEach(product => {
            const row = document.createElement('tr');
            const statusClass = product.low_stock ? 'warning' : 'success';
            const statusText = product.low_stock ? 'مخزون منخفض' : 'متوفر';
            
            // إضافة سمة data-product-id للصف
            row.setAttribute('data-product-id', product.id);
            
            row.innerHTML = `
                <td>${product.code}</td>
                <td>${product.name}</td>
                <td>${product.category_name || 'غير محدد'}</td>
                <td class="${product.low_stock ? 'low-stock' : ''}">${product.quantity}</td>
                <td>${product.selling_price} ريال</td>
                <td>${product.cost_price} ريال</td>
                <td>${product.total_value.toLocaleString()} ريال</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editProduct('${product.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct('${product.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
    }
}

// تحميل بيانات العملاء
async function loadCustomersData() {
    try {
        const response = await apiRequest('api/customers.php');
        if (response.success) {
            updateCustomersSummary(response.summary);
            updateCustomersTable(response.data);
        } else {
            showAlert('خطأ في تحميل بيانات العملاء: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في تحميل بيانات العملاء', 'danger');
    }
}

// تحديث ملخص العملاء
function updateCustomersSummary(summary) {
    const summaryContainer = document.querySelector('.customers-summary .summary-cards');
    if (summaryContainer) {
        summaryContainer.innerHTML = `
            <div class="summary-card">
                <h4>إجمالي العملاء</h4>
                <p class="summary-value">${summary.total_customers}</p>
            </div>
            <div class="summary-card">
                <h4>إجمالي الديون</h4>
                <p class="summary-value">${summary.total_debt.toLocaleString()} ريال</p>
            </div>
            <div class="summary-card">
                <h4>عملاء جدد هذا الشهر</h4>
                <p class="summary-value">${summary.new_customers_this_month}</p>
            </div>
        `;
    }
}

// تحديث جدول العملاء
function updateCustomersTable(customers) {
    const tableBody = document.querySelector('#customers tbody');
    if (tableBody) {
        tableBody.innerHTML = '';
        
        customers.forEach(customer => {
            const row = document.createElement('tr');
            const lastTransaction = customer.updated_at ? new Date(customer.updated_at).toLocaleDateString('ar-SA') : 'لا يوجد';
            
            row.innerHTML = `
                <td>${customer.code}</td>
                <td>${customer.name}</td>
                <td>${customer.city || 'غير محدد'}</td>
                <td>${customer.phone || 'غير محدد'}</td>
                <td class="debt-amount">${customer.balance.toLocaleString()} ريال</td>
                <td>${lastTransaction}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editCustomer(${customer.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewCustomer(${customer.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printCustomerCard(${customer.id})">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
    }
}

// حفظ منتج جديد
async function saveProduct(productData) {
    try {
        console.log('بدء عملية حفظ المنتج:', productData);
        
        // التحقق من وجود الملف
        const testResponse = await fetch('api/products.php', {
            method: 'GET'
        });
        
        if (!testResponse.ok) {
            throw new Error(`ملف API غير موجود أو لا يمكن الوصول إليه: ${testResponse.status}`);
        }
        
        console.log('ملف API متاح، إرسال البيانات...');
        
        const response = await fetch('api/products.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(productData)
        });

        console.log('حالة الاستجابة:', response.status);
        console.log('رؤوس الاستجابة:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('خطأ في الاستجابة:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const responseText = await response.text();
        console.log('نص الاستجابة الخام:', responseText);

        let result;
        try {
            result = JSON.parse(responseText);
        } catch (parseError) {
            console.error('خطأ في تحليل JSON:', parseError);
            console.error('النص المستلم:', responseText);
            throw new Error('الاستجابة ليست JSON صحيح');
        }

        console.log('نتيجة API:', result);

        if (result.success) {
            if (typeof loadInventoryData === 'function') {
                loadInventoryData();
            }
            return true;
        } else {
            showAlert('خطأ في إضافة المنتج: ' + (result.message || 'خطأ غير معروف'), 'danger');
            return false;
        }
    } catch (error) {
        console.error('خطأ في saveProduct:', error);
        showAlert('خطأ في الاتصال بالخادم: ' + error.message, 'danger');
        return false;
    }
}

// حفظ عميل جديد
async function saveCustomer(customerData) {
    try {
        const response = await apiRequest('api/customers.php', 'POST', customerData);
        if (response.success) {
            showAlert('تم إضافة العميل بنجاح!', 'success');
            loadCustomersData(); // إعادة تحميل البيانات
            return true;
        } else {
            showAlert('خطأ في إضافة العميل: ' + response.message, 'danger');
            return false;
        }
    } catch (error) {
        showAlert('خطأ في حفظ العميل', 'danger');
        return false;
    }
}

// البحث في المنتجات
async function searchProducts(query) {
    try {
        const response = await apiRequest(`api/products.php?search=${encodeURIComponent(query)}`);
        if (response.success) {
            updateInventoryTable(response.data);
        } else {
            showAlert('خطأ في البحث: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في البحث', 'danger');
    }
}

// البحث في العملاء
async function searchCustomers(query) {
    try {
        const response = await apiRequest(`api/customers.php?search=${encodeURIComponent(query)}`);
        if (response.success) {
            updateCustomersTable(response.data);
        } else {
            showAlert('خطأ في البحث: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في البحث', 'danger');
    }
}

// تحميل صفحة الموردين
function loadSuppliersPage() {
    const suppliersPage = document.getElementById('suppliers');
    if (suppliersPage.children.length <= 1) {
        loadSuppliersData();
        
        suppliersPage.innerHTML += `
            <div class="suppliers-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>إجمالي الموردين</h4>
                        <p class="summary-value">25</p>
                    </div>
                    <div class="summary-card">
                        <h4>إجمالي المستحقات</h4>
                        <p class="summary-value">-15,200 ريال</p>
                    </div>
                    <div class="summary-card">
                        <h4>موردين جدد هذا الشهر</h4>
                        <p class="summary-value">3</p>
                    </div>
                </div>
            </div>
            
            <div class="search-filter-bar">
                <div class="search-box">
                    <input type="text" placeholder="البحث عن مورد..." class="form-control" id="supplier-search">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم المورد</th>
                            <th>المدينة</th>
                            <th>رقم الهاتف</th>
                            <th>الرصيد</th>
                            <th>تاريخ آخر عملية</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="suppliers-table-body">
                        <!-- سيتم تحميل البيانات هنا -->
                    </tbody>
                </table>
            </div>
        `;
    }
}

// تحميل صفحة المبيعات
function loadSalesPage() {
    const salesPage = document.getElementById('sales');
    if (salesPage.children.length <= 1) {
        salesPage.innerHTML += `
            <div class="invoice-container">
                <div class="invoice-header">
                    <div class="invoice-info">
                        <h3>فاتورة مبيعات جديدة</h3>
                        <p>رقم الفاتورة: <span id="invoice-number">INV-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}-001</span></p>
                        <p>التاريخ: <span id="invoice-date">${new Date().toLocaleDateString('ar-SA')}</span></p>
                    </div>
                    <div class="customer-selection">
                        <label>العميل:</label>
                        <select class="form-control" id="customer-select" onchange="selectCustomer(this.value)">
                            <option value="">اختر العميل</option>
                            <option value="cash">عميل نقدي</option>
                        </select>
                        <button class="btn btn-sm btn-secondary" onclick="openAddCustomerModal()">
                            <i class="fas fa-plus"></i> عميل جديد
                        </button>
                    </div>
                </div>
                
                <div class="invoice-items">
                    <div class="add-item-section">
                        <div class="item-inputs">
                            <input type="text" placeholder="البحث عن منتج أو مسح الكود بار..." class="form-control" id="product-search-input" onkeyup="searchProductForInvoice(this.value)">
                            <input type="number" placeholder="الكمية" class="form-control" id="quantity-input" min="1" value="1">
                            <button class="btn btn-primary" onclick="addItemToInvoice()">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                        <div id="product-suggestions" class="suggestions-dropdown"></div>
                    </div>
                    
                    <div class="items-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoice-items-body">
                                <!-- سيتم إضافة العناصر هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="invoice-totals">
                    <div class="totals-section">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0.00 ريال</span>
                        </div>
                        <div class="total-row">
                            <span>الخصم:</span>
                            <input type="number" class="form-control discount-input" id="discount-input" value="0" onchange="calculateTotals()">
                        </div>
                        <div class="total-row">
                            <span>الضريبة (15%):</span>
                            <span id="tax-amount">0.00 ريال</span>
                        </div>
                        <div class="total-row final-total">
                            <span>الإجمالي النهائي:</span>
                            <span id="final-total">0.00 ريال</span>
                        </div>
                    </div>
                    
                    <div class="payment-section">
                        <label>طريقة الدفع:</label>
                        <select class="form-control" id="payment-method">
                            <option value="cash">نقدي</option>
                            <option value="credit">آجل</option>
                            <option value="card">بطاقة</option>
                        </select>
                        
                        <textarea class="form-control" placeholder="ملاحظات..." id="invoice-notes" rows="3"></textarea>
                        
                        <div class="invoice-actions">
                            <button class="btn btn-success" onclick="saveInvoice('sales')">
                                <i class="fas fa-save"></i> حفظ الفاتورة
                            </button>
                            <button class="btn btn-info" onclick="printInvoice()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button class="btn btn-secondary" onclick="clearInvoice()">
                                <i class="fas fa-trash"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        loadCustomersForSelect();
    }
}

// تحميل صفحة المشتريات
function loadPurchasesPage() {
    const purchasesPage = document.getElementById('purchases');
    if (purchasesPage.children.length <= 1) {
        purchasesPage.innerHTML += `
            <div class="invoice-container">
                <div class="invoice-header">
                    <div class="invoice-info">
                        <h3>فاتورة مشتريات جديدة</h3>
                        <p>رقم الفاتورة: <span id="purchase-invoice-number">PUR-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}-001</span></p>
                        <p>التاريخ: <span id="purchase-invoice-date">${new Date().toLocaleDateString('ar-SA')}</span></p>
                    </div>
                    <div class="supplier-selection">
                        <label>المورد:</label>
                        <select class="form-control" id="supplier-select" onchange="selectSupplier(this.value)">
                            <option value="">اختر المورد</option>
                        </select>
                        <button class="btn btn-sm btn-secondary" onclick="openAddSupplierModal()">
                            <i class="fas fa-plus"></i> مورد جديد
                        </button>
                    </div>
                </div>
                
                <div class="invoice-items">
                    <div class="add-item-section">
                        <div class="item-inputs">
                            <input type="text" placeholder="البحث عن منتج أو إضافة منتج جديد..." class="form-control" id="purchase-product-search" onkeyup="searchProductForPurchase(this.value)">
                            <input type="number" placeholder="الكمية" class="form-control" id="purchase-quantity" min="1" value="1">
                            <input type="number" placeholder="سعر الشراء" class="form-control" id="purchase-price" step="0.01">
                            <button class="btn btn-primary" onclick="addItemToPurchaseInvoice()">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                        <div id="purchase-product-suggestions" class="suggestions-dropdown"></div>
                    </div>
                    
                    <div class="items-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>سعر الشراء</th>
                                    <th>الإجمالي</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="purchase-items-body">
                                <!-- سيتم إضافة العناصر هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="invoice-totals">
                    <div class="totals-section">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span id="purchase-subtotal">0.00 ريال</span>
                        </div>
                        <div class="total-row">
                            <span>الخصم:</span>
                            <input type="number" class="form-control discount-input" id="purchase-discount" value="0" onchange="calculatePurchaseTotals()">
                        </div>
                        <div class="total-row">
                            <span>الضريبة (15%):</span>
                            <span id="purchase-tax">0.00 ريال</span>
                        </div>
                        <div class="total-row final-total">
                            <span>الإجمالي النهائي:</span>
                            <span id="purchase-final-total">0.00 ريال</span>
                        </div>
                    </div>
                    
                    <div class="payment-section">
                        <label>طريقة الدفع:</label>
                        <select class="form-control" id="purchase-payment-method">
                            <option value="cash">نقدي</option>
                            <option value="credit">آجل</option>
                            <option value="bank">تحويل بنكي</option>
                        </select>
                        
                        <textarea class="form-control" placeholder="ملاحظات..." id="purchase-notes" rows="3"></textarea>
                        
                        <div class="invoice-actions">
                            <button class="btn btn-success" onclick="saveInvoice('purchase')">
                                <i class="fas fa-save"></i> حفظ الفاتورة
                            </button>
                            <button class="btn btn-info" onclick="printPurchaseInvoice()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button class="btn btn-secondary" onclick="clearPurchaseInvoice()">
                                <i class="fas fa-trash"></i> مسح
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        loadSuppliersForSelect();
    }
}

// تحميل صفحة التقارير
function loadReportsPage() {
    const reportsPage = document.getElementById('reports');
    if (reportsPage.children.length <= 1) {
        reportsPage.innerHTML += `
            <div class="reports-container">
                <div class="reports-filters">
                    <div class="filter-group">
                        <label>نوع التقرير:</label>
                        <select class="form-control" id="report-type" onchange="changeReportType(this.value)">
                            <option value="sales">تقرير المبيعات</option>
                            <option value="purchases">تقرير المشتريات</option>
                            <option value="inventory">تقرير المخزون</option>
                            <option value="financial">التقرير المالي</option>
                            <option value="customers">تقرير العملاء</option>
                            <option value="suppliers">تقرير الموردين</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>من تاريخ:</label>
                        <input type="date" class="form-control" id="report-start-date" value="${new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]}">
                    </div>
                    <div class="filter-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" class="form-control" id="report-end-date" value="${new Date().toISOString().split('T')[0]}">
                    </div>
                    <div class="filter-group">
                        <button class="btn btn-primary" onclick="generateReport()">
                            <i class="fas fa-chart-bar"></i> إنشاء التقرير
                        </button>
                    </div>
                </div>
                
                <div class="report-content" id="report-content">
                    <div class="report-placeholder">
                        <i class="fas fa-chart-line"></i>
                        <h3>اختر نوع التقرير والفترة الزمنية</h3>
                        <p>سيتم عرض التقرير هنا بعد الضغط على "إنشاء التقرير"</p>
                    </div>
                </div>
            </div>
        `;
    }
}

// تحميل صفحة الفواتير السابقة
function loadInvoicesPage() {
    const invoicesPage = document.getElementById('invoices');
    if (invoicesPage.children.length <= 1) {
        invoicesPage.innerHTML += `
            <div class="invoices-container">
                <div class="invoices-filters">
                    <div class="search-box">
                        <input type="text" placeholder="البحث برقم الفاتورة أو اسم العميل..." class="form-control" id="invoices-search">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="filter-group">
                        <label>من تاريخ:</label>
                        <input type="date" class="form-control" id="invoices-start-date">
                    </div>
                    <div class="filter-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" class="form-control" id="invoices-end-date">
                    </div>
                </div>
                
                <div class="invoices-tabs">
                    <button class="tab-btn active" onclick="switchInvoiceTab('all')">جميع الفواتير</button>
                    <button class="tab-btn" onclick="switchInvoiceTab('sales')">فواتير المبيعات</button>
                    <button class="tab-btn" onclick="switchInvoiceTab('purchases')">فواتير المشتريات</button>
                </div>
                
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>النوع</th>
                                <th>العميل/المورد</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoices-table-body">
                            <!-- سيتم تحميل البيانات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        loadInvoicesData();
    }
}

// وظائف إضافية للموردين
function openAddSupplierModal() {
    showModal('إضافة مورد جديد', `
        <form id="add-supplier-form">
            <div class="form-group">
                <label class="form-label">اسم المورد</label>
                <input type="text" class="form-control" name="supplier_name" required>
            </div>
            <div class="form-group">
                <label class="form-label">رقم الهاتف</label>
                <input type="tel" class="form-control" name="phone" required>
            </div>
            <div class="form-group">
                <label class="form-label">المدينة</label>
                <input type="text" class="form-control" name="city" required>
            </div>
            <div class="form-group">
                <label class="form-label">العنوان</label>
                <textarea class="form-control" name="address" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label class="form-label">الرصيد الافتتاحي</label>
                <input type="number" class="form-control" name="initial_balance" step="0.01" value="0">
            </div>
            <div class="modal-actions">
                <button type="submit" class="btn btn-primary">حفظ المورد</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            </div>
        </form>
    `);
    
    document.getElementById('add-supplier-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const supplierData = {
            name: formData.get('supplier_name'),
            phone: formData.get('phone'),
            city: formData.get('city'),
            address: formData.get('address'),
            balance: parseFloat(formData.get('initial_balance')) || 0
        };
        
        const success = await saveSupplier(supplierData);
        if (success) {
            closeModal();
        }
    });
}

// متغيرات الفاتورة
let currentInvoiceItems = [];
let currentCustomer = null;
let currentSupplier = null;

// إضافة أنماط CSS إضافية
const additionalStyles = `
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .summary-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .summary-card h4 {
        color: #666;
        font-size: 14px;
        margin-bottom: 10px;
    }
    
    .summary-value {
        font-size: 24px;
        font-weight: 700;
        color: #333;
    }
    
    .summary-value.warning {
        color: #ffc107;
    }
    
    .search-filter-bar {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
        align-items: center;
    }
    
    .search-box {
        position: relative;
        flex: 1;
    }
    
    .search-box i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }
    
    .search-box input {
        padding-left: 45px;
    }
    
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .status-badge.success {
        background: #d4edda;
        color: #155724;
    }
    
    .status-badge.warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-badge.danger {
        background: #f8d7da;
        color: #721c24;
    }
    
    .low-stock {
        color: #dc3545;
        font-weight: 600;
    }
    
    .debt-amount {
        color: #dc3545;
        font-weight: 600;
    }
    
    .btn-sm {
        padding: 5px 10px;
        font-size: 12px;
    }
    
    .modal-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }
    
    /* أنماط الكود بار */
    .barcode-cell {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .barcode-text {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #333;
    }
    
    .barcode-input-group {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .barcode-input-group input {
        flex: 1;
    }
    
    .barcode-search-box {
        display: flex;
        gap: 8px;
        align-items: center;
        margin: 0 10px;
    }
    
    .barcode-display {
        text-align: center;
        padding: 20px;
    }
    
    .barcode-info {
        margin-bottom: 20px;
    }
    
    .barcode-visual {
        margin: 20px 0;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background: white;
    }
    
    .barcode-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }
    
    .barcode-scanner {
        text-align: center;
        padding: 20px;
    }
    
    .scanner-area {
        width: 300px;
        height: 200px;
        border: 2px dashed #667eea;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        position: relative;
        background: #f8f9ff;
    }
    
    .scanner-line {
        width: 80%;
        height: 2px;
        background: #667eea;
        animation: scan 2s infinite;
    }
    
    @keyframes scan {
        0% { transform: translateY(-50px); opacity: 0; }
        50% { opacity: 1; }
        100% { transform: translateY(50px); opacity: 0; }
    }
    
    .scanner-controls {
        display: flex;
        gap: 10px;
        justify-content: center;
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    }
    
    .notification-success {
        background: #28a745;
    }
    
    .notification-warning {
        background: #ffc107;
        color: #333;
    }
    
    .notification-error {
        background: #dc3545;
    }
    
    .notification-info {
        background: #17a2b8;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .btn-xs {
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
    }
`;

// إضافة الأنماط الإضافية
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// إضافة الأنماط للبحث والكود بار
const barcodeStyles = `
.barcode-scanner-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 2px dashed #007bff;
}

.scanner-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.scanner-input input {
    flex: 1;
    font-size: 16px;
    padding: 10px;
    border: 2px solid #007bff;
    border-radius: 5px;
}

.scanner-input input:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
}

.suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.suggestion-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-price {
    color: #28a745;
    font-weight: bold;
}

.add-item-section {
    position: relative;
    margin-bottom: 20px;
}

.item-inputs {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.item-inputs input {
    flex: 1;
}

.item-inputs button {
    white-space: nowrap;
}
`;

// إضافة الأنماط للصفحة
const existingStyle = document.querySelector('style');
if (existingStyle) {
    existingStyle.textContent += barcodeStyles;
} else {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = barcodeStyles;
    document.head.appendChild(styleSheet);
}

// وظائف الكود بار
function generateRandomBarcode() {
    const barcodeInput = document.querySelector('input[name="barcode"]');
    if (barcodeInput) {
        // توليد كود بار من 13 رقم (EAN-13 format)
        const barcode = generateEAN13();
        barcodeInput.value = barcode;
    }
}

function generateEAN13() {
    // توليد 12 رقم عشوائي
    let barcode = '';
    for (let i = 0; i < 12; i++) {
        barcode += Math.floor(Math.random() * 10);
    }
    
    // حساب رقم التحقق
    let sum = 0;
    for (let i = 0; i < 12; i++) {
        const digit = parseInt(barcode[i]);
        sum += (i % 2 === 0) ? digit : digit * 3;
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    
    return barcode + checkDigit;
}

function generateBarcode(productId, barcodeValue) {
    showModal('الكود بار للمنتج', `
        <div class="barcode-display">
            <div class="barcode-info">
                <h4>كود المنتج: ${productId}</h4>
                <p>الكود بار: ${barcodeValue}</p>
            </div>
            <div class="barcode-visual" id="barcode-${productId}">
                <svg id="barcode-svg-${productId}"></svg>
            </div>
            <div class="barcode-actions">
                <button class="btn btn-primary" onclick="printBarcode('${productId}', '${barcodeValue}')">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-secondary" onclick="downloadBarcode('${productId}', '${barcodeValue}')">
                    <i class="fas fa-download"></i> تحميل
                </button>
            </div>
        </div>
    `);
    
    // رسم الكود بار باستخدام SVG
    setTimeout(() => {
        drawBarcode(`barcode-svg-${productId}`, barcodeValue);
    }, 100);
}

function drawBarcode(svgId, barcodeValue) {
    const svg = document.getElementById(svgId);
    if (!svg || typeof JsBarcode === 'undefined') return;
    
    try {
        // استخدام مكتبة JsBarcode لرسم الكود بار
        JsBarcode(svg, barcodeValue, {
            format: "CODE128",
            width: 2,
            height: 60,
            displayValue: true,
            fontSize: 12,
            textAlign: "center",
            textPosition: "bottom",
            textMargin: 2,
            fontOptions: "",
            font: "Arial",
            fontColor: "#000000",
            background: "#ffffff",
            lineColor: "#000000",
            margin: 10
        });
    } catch (error) {
        console.error('خطأ في رسم الكود بار:', error);
        // رسم بديل بسيط في حالة الخطأ
        svg.innerHTML = `
            <rect width="200" height="80" fill="white" stroke="#ccc"/>
            <text x="100" y="40" text-anchor="middle" font-family="Arial" font-size="12">${barcodeValue}</text>
        `;
    }
}

function convertToBinary(barcode) {
    // تحويل مبسط للكود بار - يمكن تحسينه لاحق
    let binary = '';
    for (let i = 0; i < barcode.length; i++) {
        const digit = parseInt(barcode[i]);
        // نمط بسيط لكل رقم
        const patterns = [
            '0001101', '0011001', '0010011', '0111101', '0100011',
            '0110001', '0101111', '0111011', '0110111', '0001011'
        ];
        binary += patterns[digit] || '0001101';
    }
    return binary;
}

function printBarcode(productId, barcodeValue) {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة الكود بار - ${productId}</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; margin: 20px; }
                .barcode-print { margin: 20px; padding: 20px; border: 1px solid #ccc; }
                svg { margin: 10px; }
            </style>
        </head>
        <body>
            <div class="barcode-print">
                <h3>كود المنتج: ${productId}</h3>
                <div id="print-barcode"></div>
                <p>الكود بار: ${barcodeValue}</p>
            </div>
            <script>
                // نسخ SVG من النافذة الأصلية
                const originalSvg = window.opener.document.getElementById('barcode-svg-${productId}');
                if (originalSvg) {
                    document.getElementById('print-barcode').innerHTML = originalSvg.outerHTML;
                }
                window.print();
            </script>
        </body>
        </html>
    `);
}

function downloadBarcode(productId, barcodeValue) {
    const svg = document.getElementById(`barcode-svg-${productId}`);
    if (!svg) return;
    
    // تحويل SVG إلى صورة
    const svgData = new XMLSerializer().serializeToString(svg);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = function() {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        // تحميل الصورة
        const link = document.createElement('a');
        link.download = `barcode-${productId}.png`;
        link.href = canvas.toDataURL();
        link.click();
    };
    
    img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
}

function scanBarcode() {
    // محاكاة مسح الكود بار - يمكن دمج مكتبة حقيقية لاحق
    showModal('مسح الكود بار', `
        <div class="barcode-scanner">
            <div class="scanner-area">
                <div class="scanner-line"></div>
                <p>وجه الكاميرا نحو الكود بار</p>
            </div>
            <div class="scanner-controls">
                <button class="btn btn-primary" onclick="simulateBarcodeScan()">
                    محاكاة المسح
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">
                    إلغاء
                </button>
            </div>
        </div>
    `);
}

function simulateBarcodeScan() {
    // محاكاة نتيجة المسح
    const simulatedBarcode = generateEAN13();
    const barcodeInput = document.querySelector('input[name="barcode"]');
    if (barcodeInput) {
        barcodeInput.value = simulatedBarcode;
    }
    closeModal();
    showNotification('تم مسح الكود بار بنجاح!', 'success');
}

function scanBarcodeForSearch() {
    showModal('البحث بالكود بار', `
        <div class="barcode-scanner">
            <div class="scanner-area">
                <div class="scanner-line"></div>
                <p>وجه الكاميرا نحو الكود بار للبحث</p>
            </div>
            <div class="scanner-controls">
                <button class="btn btn-primary" onclick="simulateBarcodeSearch()">
                    محاكاة البحث
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">
                    إلغاء
                </button>
            </div>
        </div>
    `);
}

function simulateBarcodeSearch() {
    // محاكاة البحث بالكود بار
    const barcodeSearchInput = document.getElementById('barcode-search');
    if (barcodeSearchInput) {
        barcodeSearchInput.value = '1234567890123';
        searchProductsByBarcode('1234567890123');
    }
    closeModal();
}

// تحسين وظيفة حفظ المنتج
async function saveProduct(productData) {
    try {
        console.log('إرسال بيانات المنتج:', productData); // للتشخيص
        
        const response = await fetch('api/products.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(productData)
        });

        console.log('استجابة الخادم:', response.status); // للتشخيص

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('نتيجة API:', result); // للتشخيص

        if (result.success) {
            showAlert('تم إضافة المنتج بنجاح!', 'success');
            loadInventoryData(); // إعادة تحميل البيانات
            return true;
        } else {
            showAlert('خطأ في إضافة المنتج: ' + (result.message || 'خطأ غير معروف'), 'danger');
            return false;
        }
    } catch (error) {
        console.error('خطأ في saveProduct:', error);
        showAlert('خطأ في الاتصال بالخادم: ' + error.message, 'danger');
        return false;
    }
}

// تحسين وظيفة apiRequest
async function apiRequest(url, method = 'GET', data = null) {
    try {
        console.log(`API Request: ${method} ${url}`, data); // للتشخيص
        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        
        console.log(`API Response Status: ${response.status}`); // للتشخيص
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const text = await response.text();
            console.error('Response is not JSON:', text);
            throw new Error('الاستجابة ليست بصيغة JSON صحيحة');
        }
        
        const result = await response.json();
        console.log('API Result:', result); // للتشخيص
        
        return result;
    } catch (error) {
        console.error('API Error:', error);
        return {
            success: false,
            message: 'خطأ في الاتصال بالخادم: ' + error.message
        };
    }
}

// تحسين معالج نموذج إضافة المنتج
function setupAddProductForm() {
    const form = document.getElementById('add-product-form');
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            
            // التحقق من البيانات المطلوبة
            const productName = formData.get('product_name');
            if (!productName || productName.trim() === '') {
                showAlert('اسم المنتج مطلوب', 'warning');
                return;
            }
            
            const productData = {
                name: productName.trim(),
                description: formData.get('description') || '',
                category_id: formData.get('category') || null,
                quantity: parseInt(formData.get('quantity')) || 0,
                selling_price: parseFloat(formData.get('price')) || 0,
                cost_price: parseFloat(formData.get('cost')) || 0,
                min_stock: parseInt(formData.get('min_stock')) || 0,
                barcode: formData.get('barcode') || '',
                code: formData.get('code') || 'P' + Date.now()
            };
            
            console.log('بيانات المنتج المرسلة:', productData);
            
            const success = await saveProduct(productData);
            if (success) {
                closeModal();
                this.reset(); // إعادة تعيين النموذج
            }
        } catch (error) {
function searchProductsByBarcode(barcode) {
    // البحث في المنتجات بالكود بار
    fetch(`api/products.php?barcode=${barcode}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.length > 0) {
                // عرض النتائج
                displaySearchResults(data.data);
                showNotification(`تم العثور على ${data.data.length} منتج`, 'success');
            } else {
                showNotification('لم يتم العثور على منتجات بهذا الكود بار', 'warning');
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showNotification('حدث خطأ أثناء البحث', 'error');
        });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// إضافة معالجات البحث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // معالج البحث في المنتجات
    document.addEventListener('input', function(e) {
        if (e.target.id === 'product-search') {
            const query = e.target.value.trim();
            if (query.length > 2) {
                searchProducts(query);
            } else if (query.length === 0) {
                loadInventoryData();
            }
        }
        
        // معالج البحث بالكود بار
        if (e.target.id === 'barcode-search') {
            const barcode = e.target.value.trim();
            if (barcode.length >= 8) {
                searchProductsByBarcode(barcode);
            } else if (barcode.length === 0) {
                loadInventoryData();
            }
        }
        
        // معالج البحث في العملاء
        if (e.target.id === 'customer-search') {
            const query = e.target.value.trim();
            if (query.length > 2) {
                searchCustomers(query);
            } else if (query.length === 0) {
                loadCustomersData();
            }
        }
    });
});

// وظائف إضافية للفواتير والتقارير

// تحميل بيانات الموردين
async function loadSuppliersData() {
    try {
        const response = await apiRequest('api/suppliers.php');
        if (response.success) {
            updateSuppliersTable(response.data);
            updateSuppliersSummary(response.summary);
        } else {
            showAlert('خطأ في تحميل بيانات الموردين: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في تحميل بيانات الموردين', 'danger');
    }
}

// تحديث جدول الموردين
function updateSuppliersTable(suppliers) {
    const tableBody = document.getElementById('suppliers-table-body');
    if (tableBody) {
        tableBody.innerHTML = '';
        
        suppliers.forEach(supplier => {
            const row = document.createElement('tr');
            const lastTransaction = supplier.updated_at ? new Date(supplier.updated_at).toLocaleDateString('ar-SA') : 'لا يوجد';
            
            row.innerHTML = `
                <td>${supplier.code}</td>
                <td>${supplier.name}</td>
                <td>${supplier.city || 'غير محدد'}</td>
                <td>${supplier.phone || 'غير محدد'}</td>
                <td class="debt-amount">${supplier.balance.toLocaleString()} ريال</td>
                <td>${lastTransaction}</td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editSupplier(${supplier.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewSupplier(${supplier.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="printSupplierCard(${supplier.id})">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
    }
}

// تحديث ملخص الموردين
function updateSuppliersSummary(summary) {
    const summaryContainer = document.querySelector('.suppliers-summary .summary-cards');
    if (summaryContainer) {
        summaryContainer.innerHTML = `
            <div class="summary-card">
                <h4>إجمالي الموردين</h4>
                <p class="summary-value">${summary.total_suppliers}</p>
            </div>
            <div class="summary-card">
                <h4>إجمالي المستحقات</h4>
                <p class="summary-value">${summary.total_debt.toLocaleString()} ريال</p>
            </div>
            <div class="summary-card">
                <h4>موردين جدد هذا الشهر</h4>
                <p class="summary-value">${summary.new_suppliers_this_month}</p>
            </div>
        `;
    }
}

// حفظ مورد جديد
async function saveSupplier(supplierData) {
    try {
        const response = await apiRequest('api/suppliers.php', 'POST', supplierData);
        if (response.success) {
            showAlert('تم إضافة المورد بنجاح!', 'success');
            loadSuppliersData();
            return true;
        } else {
            showAlert('خطأ في إضافة المورد: ' + response.message, 'danger');
            return false;
        }
    } catch (error) {
        showAlert('خطأ في حفظ المورد', 'danger');
        return false;
    }
}

// تحميل العملاء للاختيار في الفاتورة
async function loadCustomersForSelect() {
    try {
        const response = await apiRequest('api/customers.php');
        if (response.success) {
            const customerSelect = document.getElementById('customer-select');
            if (customerSelect) {
                customerSelect.innerHTML = '<option value="">اختر العميل</option><option value="cash">عميل نقدي</option>';
                response.data.forEach(customer => {
                    customerSelect.innerHTML += `<option value="${customer.id}">${customer.name}</option>`;
                });
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل العملاء:', error);
    }
}

// تحميل الموردين للاختيار في الفاتورة
async function loadSuppliersForSelect() {
    try {
        const response = await apiRequest('api/suppliers.php');
        if (response.success) {
            const supplierSelect = document.getElementById('supplier-select');
            if (supplierSelect) {
                supplierSelect.innerHTML = '<option value="">اختر المورد</option>';
                response.data.forEach(supplier => {
                    supplierSelect.innerHTML += `<option value="${supplier.id}">${supplier.name}</option>`;
                });
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل الموردين:', error);
    }
}

// البحث عن منتج للفاتورة
function searchProductForInvoice(query) {
    if (query.length < 2) {
        document.getElementById('product-suggestions').innerHTML = '';
        return;
    }
    
    fetch(`api/products.php?search=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProductSuggestions(data.data, 'product-suggestions');
            }
        })
        .catch(error => console.error('خطأ في البحث:', error));
}

// عرض اقتراحات المنتجات
function displayProductSuggestions(products, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    if (products.length === 0) {
        container.innerHTML = '<div class="suggestion-item">لا توجد منتجات</div>';
        return;
    }
    
    container.innerHTML = products.map(product => `
        <div class="suggestion-item" onclick="selectProductForInvoice(${product.id}, '${product.name}', ${product.selling_price})">
            <strong>${product.name}</strong>
            <span class="suggestion-price">${product.selling_price} ريال</span>
            <small>المتوفر: ${product.quantity}</small>
        </div>
    `).join('');
}

// اختيار منتج للفاتورة
function selectProductForInvoice(productId, productName, price) {
    document.getElementById('product-search-input').value = productName;
    document.getElementById('product-suggestions').innerHTML = '';
    
    // حفظ بيانات المنتج المختار
    window.selectedProduct = {
        id: productId,
        name: productName,
        price: price
    };
}

// إضافة عنصر للفاتورة
function addItemToInvoice() {
    const product = window.selectedProduct;
    const quantity = parseInt(document.getElementById('quantity-input').value) || 1;
    
    if (!product) {
        showAlert('يرجى اختيار منتج أولاً', 'warning');
        return;
    }
    
    const existingItemIndex = currentInvoiceItems.findIndex(item => item.product_id === product.id);
    
    if (existingItemIndex >= 0) {
        currentInvoiceItems[existingItemIndex].quantity += quantity;
        currentInvoiceItems[existingItemIndex].total_price = currentInvoiceItems[existingItemIndex].quantity * product.price;
    } else {
        currentInvoiceItems.push({
            product_id: product.id,
            product_name: product.name,
            quantity: quantity,
            unit_price: product.price,
            total_price: quantity * product.price
        });
    }
    
    updateInvoiceTable();
    calculateTotals();
    
    // مسح الحقول
    document.getElementById('product-search-input').value = '';
    document.getElementById('quantity-input').value = '1';
    window.selectedProduct = null;
}

// تحديث جدول الفاتورة
function updateInvoiceTable() {
    const tableBody = document.getElementById('invoice-items-body');
    if (!tableBody) return;
    
    tableBody.innerHTML = currentInvoiceItems.map((item, index) => `
        <tr>
            <td>${item.product_name}</td>
            <td>
                <input type="number" class="form-control" value="${item.quantity}" 
                       onchange="updateItemQuantity(${index}, this.value)" min="1">
            </td>
            <td>${item.unit_price.toFixed(2)} ريال</td>
            <td>${item.total_price.toFixed(2)} ريال</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="removeItemFromInvoice(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// تحديث كمية عنصر
function updateItemQuantity(index, newQuantity) {
    const quantity = parseInt(newQuantity) || 1;
    currentInvoiceItems[index].quantity = quantity;
    currentInvoiceItems[index].total_price = quantity * currentInvoiceItems[index].unit_price;
    
    updateInvoiceTable();
    calculateTotals();
}

// إزالة عنصر من الفاتورة
function removeItemFromInvoice(index) {
    currentInvoiceItems.splice(index, 1);
    updateInvoiceTable();
    calculateTotals();
}

// حساب الإجماليات
function calculateTotals() {
    const subtotal = currentInvoiceItems.reduce((sum, item) => sum + item.total_price, 0);
    const discount = parseFloat(document.getElementById('discount-input')?.value || 0);
    const taxRate = 0.15; // 15% ضريبة
    const taxAmount = (subtotal - discount) * taxRate;
    const finalTotal = subtotal - discount + taxAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ريال';
    document.getElementById('tax-amount').textContent = taxAmount.toFixed(2) + ' ريال';
    document.getElementById('final-total').textContent = finalTotal.toFixed(2) + ' ريال';
}

// حفظ الفاتورة
async function saveInvoice(type) {
    if (currentInvoiceItems.length === 0) {
        showAlert('يرجى إضافة عناصر للفاتورة', 'warning');
        return;
    }
    
    const subtotal = currentInvoiceItems.reduce((sum, item) => sum + item.total_price, 0);
    const discount = parseFloat(document.getElementById('discount-input')?.value || 0);
    const taxAmount = (subtotal - discount) * 0.15;
    const finalTotal = subtotal - discount + taxAmount;
    
    const invoiceData = {
        customer_id: type === 'sales' ? document.getElementById('customer-select')?.value : null,
        supplier_id: type === 'purchase' ? document.getElementById('supplier-select')?.value : null,
        total_amount: subtotal,
        discount: discount,
        tax_amount: taxAmount,
        final_amount: finalTotal,
        payment_method: document.getElementById(type === 'sales' ? 'payment-method' : 'purchase-payment-method')?.value,
        notes: document.getElementById(type === 'sales' ? 'invoice-notes' : 'purchase-notes')?.value,
        items: currentInvoiceItems
    };
    
    try {
        const apiUrl = type === 'sales' ? 'api/sales.php' : 'api/purchases.php';
        const response = await apiRequest(apiUrl, 'POST', invoiceData);
        
        if (response.success) {
            showAlert(`تم حفظ فاتورة ${type === 'sales' ? 'المبيعات' : 'المشتريات'} بنجاح!`, 'success');
            clearInvoice();
        } else {
            showAlert('خطأ في حفظ الفاتورة: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في حفظ الفاتورة', 'danger');
    }
}

// مسح الفاتورة
function clearInvoice() {
    currentInvoiceItems = [];
    updateInvoiceTable();
    calculateTotals();
    
    // مسح الحقول
    document.getElementById('customer-select').value = '';
    document.getElementById('product-search-input').value = '';
    document.getElementById('quantity-input').value = '1';
    document.getElementById('discount-input').value = '0';
    document.getElementById('payment-method').value = 'cash';
    document.getElementById('invoice-notes').value = '';
}

// إنشاء التقارير
async function generateReport() {
    const reportType = document.getElementById('report-type').value;
    const startDate = document.getElementById('report-start-date').value;
    const endDate = document.getElementById('report-end-date').value;
    
    try {
        const response = await apiRequest(`api/reports.php?type=${reportType}&start_date=${startDate}&end_date=${endDate}`);
        
        if (response.success) {
            displayReport(response.data, reportType);
        } else {
            showAlert('خطأ في إنشاء التقرير: ' + response.message, 'danger');
        }
    } catch (error) {
        showAlert('خطأ في إنشاء التقرير', 'danger');
    }
}

// عرض التقرير
function displayReport(data, reportType) {
    const reportContent = document.getElementById('report-content');
    if (!reportContent) return;
    
    let html = '';
    
    switch (reportType) {
        case 'sales':
            html = generateSalesReportHTML(data);
            break;
        case 'inventory':
            html = generateInventoryReportHTML(data);
            break;
        case 'financial':
            html = generateFinancialReportHTML(data);
            break;
        default:
            html = '<p>نوع التقرير غير مدعوم</p>';
    }
    
    reportContent.innerHTML = html;
}

// إنشاء HTML لتقرير المبيعات
function generateSalesReportHTML(data) {
    return `
        <div class="report-summary">
            <h3>ملخص المبيعات</h3>
            <div class="summary-cards">
                <div class="summary-card">
                    <h4>إجمالي الفواتير</h4>
                    <p class="summary-value">${data.summary.total_invoices}</p>
                </div>
                <div class="summary-card">
                    <h4>إجمالي المبيعات</h4>
                    <p class="summary-value">${parseFloat(data.summary.net_sales || 0).toLocaleString()} ريال</p>
                </div>
                <div class="summary-card">
                    <h4>متوسط الفاتورة</h4>
                    <p class="summary-value">${parseFloat(data.summary.average_invoice || 0).toLocaleString()} ريال</p>
                </div>
            </div>
        </div>
        
        <div class="report-section">
            <h4>أفضل المنتجات مبيعاً</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية المباعة</th>
                        <th>إجمالي المبيعات</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.top_products.map(product => `
                        <tr>
                            <td>${product.product_name}</td>
                            <td>${product.total_quantity}</td>
                            <td>${parseFloat(product.total_sales).toLocaleString()} ريال</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// تحميل بيانات الفواتير السابقة
async function loadInvoicesData() {
    try {
        const [salesResponse, purchasesResponse] = await Promise.all([
            apiRequest('api/sales.php'),
            apiRequest('api/purchases.php')
        ]);
        
        const allInvoices = [];
        
        if (salesResponse.success) {
            salesResponse.data.forEach(invoice => {
                allInvoices.push({
                    ...invoice,
                    type: 'sales',
                    type_name: 'مبيعات'
                });
            });
        }
        
        if (purchasesResponse.success) {
            purchasesResponse.data.forEach(invoice => {
                allInvoices.push({
                    ...invoice,
                    type: 'purchases',
                    type_name: 'مشتريات'
                });
            });
        }
        
        // ترتيب حسب التاريخ
        allInvoices.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        updateInvoicesTable(allInvoices);
    } catch (error) {
        showAlert('خطأ في تحميل بيانات الفواتير', 'danger');
    }
}

// تحديث جدول الفواتير
function updateInvoicesTable(invoices) {
    const tableBody = document.getElementById('invoices-table-body');
    if (!tableBody) return;
    
    tableBody.innerHTML = invoices.map(invoice => `
        <tr>
            <td>${invoice.invoice_number}</td>
            <td><span class="badge badge-${invoice.type === 'sales' ? 'success' : 'info'}">${invoice.type_name}</span></td>
            <td>${invoice.customer_name || invoice.supplier_name || 'غير محدد'}</td>
            <td>${new Date(invoice.created_at).toLocaleDateString('ar-SA')}</td>
            <td>${parseFloat(invoice.final_amount).toLocaleString()} ريال</td>
            <td>${invoice.payment_method === 'cash' ? 'نقدي' : invoice.payment_method === 'credit' ? 'آجل' : 'أخرى'}</td>
            <td><span class="badge badge-success">مكتملة</span></td>
            <td>
                <button class="btn btn-sm btn-info" onclick="viewInvoiceDetails(${invoice.id}, '${invoice.type}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-secondary" onclick="printInvoiceDetails(${invoice.id}, '${invoice.type}')">
                    <i class="fas fa-print"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// وظائف إضافية للموردين
function editSupplier(supplierId) {
    showAlert('فتح نافذة تعديل المورد: ' + supplierId, 'info');
}

function viewSupplier(supplierId) {
    showAlert('عرض تفاصيل المورد: ' + supplierId, 'info');
}

function printSupplierCard(supplierId) {
    showAlert('طباعة بطاقة المورد: ' + supplierId, 'info');
}

// وظائف إضافية للتقارير
function exportReport() {
    showAlert('تصدير التقرير', 'info');
}

function printReport() {
    window.print();
}

// وظائف إضافية للفواتير
function newSalesInvoice() {
    clearInvoice();
    showAlert('فاتورة مبيعات جديدة', 'info');
}

function newPurchaseInvoice() {
    clearInvoice();
    showAlert('فاتورة مشتريات جديدة', 'info');
}

function loadSalesHistory() {
    showAlert('تحميل سجل المبيعات', 'info');
}

function loadPurchasesHistory() {
    showAlert('تحميل سجل المشتريات', 'info');
}

// إضافة صفحة فواتير المبيعات مع دعم الكود بار
function showSalesPage() {
    const content = document.getElementById('main-content');
    content.innerHTML = `
        <div class="page-header">
            <h2><i class="fas fa-receipt"></i> فواتير المبيعات</h2>
            <button class="btn btn-primary" onclick="showNewSalesInvoice()">
                <i class="fas fa-plus"></i> فاتورة جديدة
            </button>
        </div>
        
        <div class="barcode-scanner-section">
            <div class="scanner-input">
                <input type="text" id="barcode-scanner-input" class="form-control" 
                       placeholder="امسح الكود بار أو اكتبه هنا..." 
                       onkeypress="handleBarcodeInput(event)">
                <button class="btn btn-info" onclick="startCameraScanner()">
                    <i class="fas fa-camera"></i> مسح بالكاميرا
                </button>
            </div>
        </div>
        
        <div id="current-invoice" style="display: none;">
            <div class="invoice-header">
                <h3>فاتورة مبيعات جديدة</h3>
                <div class="invoice-info">
                    <div class="form-group">
                        <label>العميل:</label>
                        <select id="customer-select" class="form-control">
                            <option value="">اختر العميل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>تاريخ الفاتورة:</label>
                        <input type="date" id="invoice-date" class="form-control" value="${new Date().toISOString().split('T')[0]}">
                    </div>
                </div>
            </div>
            
            <div class="invoice-items">
                <table class="table" id="invoice-items-table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكود بار</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                            <th>إجراءات</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            
            <div class="invoice-totals">
                <div class="totals-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0.00 ريال</span>
                </div>
                <div class="totals-row">
                    <span>الضريبة (15%):</span>
                    <span id="tax-amount">0.00 ريال</span>
                </div>
                <div class="totals-row total">
                    <span>المجموع الكلي:</span>
                    <span id="total-amount">0.00 ريال</span>
                </div>
            </div>
            
            <div class="invoice-actions">
                <button class="btn btn-success" onclick="saveInvoice()">
                    <i class="fas fa-save"></i> حفظ الفاتورة
                </button>
                <button class="btn btn-info" onclick="printInvoice()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-secondary" onclick="cancelInvoice()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </div>
    `;
    
    loadCustomers();
}

function handleBarcodeInput(event) {
    if (event.key === 'Enter') {
        const barcode = event.target.value.trim();
        if (barcode) {
            handleBarcodeScanned(barcode);
            event.target.value = '';
        }
    }
}

function showNewSalesInvoice() {
    document.getElementById('current-invoice').style.display = 'block';
    
    // إضافة حقل مسح الباركود
    const barcodeInput = document.createElement('div');
    barcodeInput.className = 'form-group';
    barcodeInput.innerHTML = `
        <label>مسح باركود المنتج:</label>
        <input type="text" id="barcode-scanner" class="form-control" 
               placeholder="ضع المؤشر هنا وامسح الباركود" autofocus>
    `;
    document.querySelector('.invoice-header').appendChild(barcodeInput);
    
    // إضافة مستمع لأحداث الباركود
    document.getElementById('barcode-scanner').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const barcode = this.value.trim();
            if (barcode) {
                fetch(`/api/barcode.php?validate&barcode=${barcode}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.valid) {
                            fetch(`/api/products.php?barcode=${barcode}`)
                                .then(response => response.json())
                                .then(product => {
                                    if (product) {
                                        addProductToInvoice(product);
                                        this.value = '';
                                    } else {
                                        showAlert('المنتج غير موجود في النظام', 'error');
                                    }
                                });
                        } else {
                            showAlert('باركود غير صحيح: ' + data.message, 'error');
                        }
                    });
            }
        }
    });
}

function updateInvoiceItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.cells[3].textContent) || 0;
    const total = quantity * price;
    
    row.querySelector('.item-total').textContent = total.toFixed(2);
    updateInvoiceTotal();
}

function updateInvoiceTotal() {
    const rows = document.querySelectorAll('#invoice-items-table tbody tr');
    let subtotal = 0;
    
    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(row.cells[3].textContent) || 0;
        const total = quantity * price;
        row.querySelector('.item-total').textContent = total.toFixed(2);
        subtotal += total;
    });
    
    const taxRate = 0.15;
    const taxAmount = subtotal * taxRate;
    const totalAmount = subtotal + taxAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('tax-amount').textContent = taxAmount.toFixed(2);
    document.getElementById('total-amount').textContent = totalAmount.toFixed(2);
}

// وظيفة لاختبار الاتصال بالخادم
async function testServerConnection() {
    try {
        const response = await fetch('api/products.php');
        const result = await response.json();
        console.log('اختبار الاتصال:', result);
        return true;
    } catch (error) {
        console.error('فشل اختبار الاتصال:', error);
        showAlert('لا يمكن الاتصال بالخادم. تحقق من إعدادات الخادم.', 'danger');
        return false;
    }
}

// استدعاء الاختبار عند تحميل الصفحة
document.addEventListener
        const total = parseFloat(row.querySelector('.item-total').textContent) || 0;
        subtotal += total;
    });
    
    const taxRate = 0.15;
    const taxAmount = subtotal * taxRate;
    const totalAmount = subtotal + taxAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ريال';
    document.getElementById('tax-amount').textContent = taxAmount.toFixed(2) + ' ريال';
    document.getElementById('total-amount').textContent = totalAmount.toFixed(2) + ' ريال';
}

function removeInvoiceItem(button) {
    button.closest('tr').remove();
    updateInvoiceTotal();
}

// إضافة وظائف الكود بار المحسنة
function initBarcodeScanner() {
    // إضافة مستمع لأحداث لوحة المفاتيح لماسح الكود بار
    let barcodeBuffer = '';
    let barcodeTimeout;
    
    document.addEventListener('keydown', function(e) {
        // إذا كان المستخدم يكتب في حقل إدخال، تجاهل
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }
        
        // مسح المؤقت السابق
        clearTimeout(barcodeTimeout);
        
        // إضافة الحرف إلى المخزن المؤقت
        if (e.key.length === 1) {
            barcodeBuffer += e.key;
        }
        
        // إعداد مؤقت جديد
        barcodeTimeout = setTimeout(() => {
            if (barcodeBuffer.length >= 8) {
                handleBarcodeScanned(barcodeBuffer);
            }
            barcodeBuffer = '';
        }, 100);
        
        // إذا تم الضغط على Enter، معالجة الكود فور
        if (e.key === 'Enter' && barcodeBuffer.length >= 8) {
            clearTimeout(barcodeTimeout);
            handleBarcodeScanned(barcodeBuffer);
            barcodeBuffer = '';
            e.preventDefault();
        }
    });
}

async function handleBarcodeScanned(barcode) {
    try {
        showAlert('تم مسح الكود بار: ' + barcode, 'info');
        
        // البحث عن المنتج
        const response = await fetch(`api/products.php?barcode=${encodeURIComponent(barcode)}`);
        const result = await response.json();
        
        if (result.success && result.data.length > 0) {
            const product = result.data[0];
            
            // إذا كنا في صفحة الفواتير، إضافة المنتج للفاتورة
            if (window.location.hash === '#sales' || window.location.hash === '#purchases') {
                addProductToInvoice(product);
            } else {
                // عرض تفاصيل المنتج
                showProductDetails(product);
            }
        } else {
            showAlert('لم يتم العثور على منتج بهذا الكود بار', 'warning');
        }
    } catch (error) {
        console.error('خطأ في معالجة الكود بار:', error);
        showAlert('خطأ في معالجة الكود بار', 'error');
    }
}

// إضافة منتج للفاتورة عبر الكود بار
function addProductToInvoice(product) {
    const invoiceTable = document.getElementById('invoice-items-table');
    if (!invoiceTable) return;
    
    const tbody = invoiceTable.querySelector('tbody');
    const existingRow = tbody.querySelector(`tr[data-product-id="${product.id}"]`);
    
    if (existingRow) {
        // زيادة الكمية إذا كان المنتج موجود
        const quantityInput = existingRow.querySelector('.quantity-input');
        quantityInput.value = parseInt(quantityInput.value) + 1;
        updateInvoiceItemTotal(existingRow);
    } else {
        // إضافة صف جديد
        const row = document.createElement('tr');
        row.setAttribute('data-product-id', product.id);
        row.innerHTML = `
            <td>${product.name}</td>
            <td>${product.barcode}</td>
            <td><input type="number" class="form-control quantity-input" value="1" min="1" onchange="updateInvoiceItemTotal(this.closest('tr'))"></td>
            <td>${product.selling_price.toFixed(2)}</td>
            <td class="item-total">${product.selling_price.toFixed(2)}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="removeInvoiceItem(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    }
    
    updateInvoiceTotal();
    showAlert(`تم إضافة ${product.name} للفاتورة`, 'success');
}

// تحسين وظيفة توليد الكود بار
async function generateRandomBarcode() {
    try {
        const response = await fetch('api/barcode.php?generate=1&type=EAN13');
        const result = await response.json();
        
        if (result.success) {
            const barcodeInput = document.querySelector('input[name="barcode"]');
            if (barcodeInput) {
                barcodeInput.value = result.barcode;
                
                // التحقق من صحة الكود
                const validation = await validateBarcode(result.barcode);
                if (validation.valid) {
                    showAlert('تم توليد كود بار صحيح', 'success');
                } else {
                    showAlert('خطأ في توليد الكود بار', 'error');
                }
            }
        }
    } catch (error) {
        console.error('خطأ في توليد الكود بار:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    }
}

// التحقق من صحة الكود بار
async function validateBarcode(barcode) {
    try {
        const response = await fetch(`api/barcode.php?validate=1&barcode=${encodeURIComponent(barcode)}`);
        return await response.json();
    } catch (error) {
        return { valid: false, message: 'خطأ في التحقق' };
    }
}

// تهيئة ماسح الكود بار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initBarcodeScanner();
});
