<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class ReportsAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->connect();
    }

    public function getDashboardStats() {
        try {
            $stats = [];

            // إحصائيات المبيعات
            $sql = "SELECT 
                        COUNT(*) as total_invoices,
                        SUM(final_amount) as total_sales,
                        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN final_amount ELSE 0 END) as today_sales,
                        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_invoices
                    FROM sales_invoices";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['sales'] = $stmt->fetch(PDO::FETCH_ASSOC);

            // إحصائيات المشتريات
            $sql = "SELECT 
                        COUNT(*) as total_invoices,
                        SUM(final_amount) as total_purchases,
                        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN final_amount ELSE 0 END) as today_purchases
                    FROM purchase_invoices";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['purchases'] = $stmt->fetch(PDO::FETCH_ASSOC);

            // إحصائيات المخزون
            $sql = "SELECT 
                        COUNT(*) as total_products,
                        SUM(quantity * selling_price) as inventory_value,
                        COUNT(CASE WHEN quantity <= min_stock THEN 1 END) as low_stock_products
                    FROM products WHERE is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['inventory'] = $stmt->fetch(PDO::FETCH_ASSOC);

            // إحصائيات العملاء والموردين
            $sql = "SELECT COUNT(*) as total_customers, SUM(balance) as total_customer_debt FROM customers WHERE is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['customers'] = $stmt->fetch(PDO::FETCH_ASSOC);

            $sql = "SELECT COUNT(*) as total_suppliers, SUM(balance) as total_supplier_debt FROM suppliers WHERE is_active = 1";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $stats['suppliers'] = $stmt->fetch(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => $stats
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في جلب الإحصائيات: ' . $e->getMessage()
            ];
        }
    }

    public function getSalesReport($startDate, $endDate) {
        try {
            // ملخص المبيعات
            $sql = "SELECT 
                        COUNT(*) as total_invoices,
                        SUM(total_amount) as gross_sales,
                        SUM(discount) as total_discount,
                        SUM(tax_amount) as total_tax,
                        SUM(final_amount) as net_sales,
                        AVG(final_amount) as average_invoice
                    FROM sales_invoices 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':start_date', $startDate);
            $stmt->bindParam(':end_date', $endDate);
            $stmt->execute();
            $summary = $stmt->fetch(PDO::FETCH_ASSOC);

            // المبيعات اليومية
            $sql = "SELECT 
                        DATE(created_at) as sale_date,
                        COUNT(*) as invoices_count,
                        SUM(final_amount) as daily_sales
                    FROM sales_invoices 
                    WHERE DATE(created_at) BETWEEN :start_date AND :end_date
                    GROUP BY DATE(created_at)
                    ORDER BY sale_date";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':start_date', $startDate);
            $stmt->bindParam(':end_date', $endDate);
            $stmt->execute();
            $dailySales = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // أفضل المنتجات مبيعاً
            $sql = "SELECT 
                        p.name as product_name,
                        p.code as product_code,
                        SUM(sii.quantity) as total_quantity,
                        SUM(sii.total_price) as total_sales,
                        AVG(sii.unit_price) as average_price
                    FROM sales_invoice_items sii
                    LEFT JOIN products p ON sii.product_id = p.id
                    LEFT JOIN sales_invoices si ON sii.invoice_id = si.id
                    WHERE DATE(si.created_at) BETWEEN :start_date AND :end_date
                    GROUP BY sii.product_id
                    ORDER BY total_sales DESC
                    LIMIT 10";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':start_date', $startDate);
            $stmt->bindParam(':end_date', $endDate);
            $stmt->execute();
            $topProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // أفضل العملاء
            $sql = "SELECT 
                        c.name as customer_name,
                        c.code as customer_code,
                        COUNT(si.id) as total_invoices,
                        SUM(si.final_amount) as total_purchases
                    FROM sales_invoices si
                    LEFT JOIN customers c ON si.customer_id = c.id
                    WHERE DATE(si.created_at) BETWEEN :start_date AND :end_date
                    GROUP BY si.customer_id
                    ORDER BY total_purchases DESC
                    LIMIT 10";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':start_date', $startDate);
            $stmt->bindParam(':end_date', $endDate);
            $stmt->execute();
            $topCustomers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => [
                    'summary' => $summary,
                    'daily_sales' => $dailySales,
                    'top_products' => $topProducts,
                    'top_customers' => $topCustomers
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء تقرير المبيعات: ' . $e->getMessage()
            ];
        }
    }

    public function getInventoryReport() {
        try {
            // ملخص المخزون
            $sql = "SELECT 
                        COUNT(*) as total_products,
                        SUM(quantity) as total_quantity,
                        SUM(quantity * cost_price) as total_cost_value,
                        SUM(quantity * selling_price) as total_selling_value,
                        COUNT(CASE WHEN quantity <= min_stock THEN 1 END) as low_stock_count,
                        COUNT(CASE WHEN quantity = 0 THEN 1 END) as out_of_stock_count
                    FROM products WHERE is_active = 1";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $summary = $stmt->fetch(PDO::FETCH_ASSOC);

            // المنتجات منخفضة المخزون
            $sql = "SELECT 
                        p.code,
                        p.name,
                        c.name as category_name,
                        p.quantity,
                        p.min_stock,
                        p.selling_price,
                        (p.quantity * p.selling_price) as total_value
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1 AND p.quantity <= p.min_stock
                    ORDER BY p.quantity ASC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $lowStockProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // المنتجات حسب الفئة
            $sql = "SELECT 
                        c.name as category_name,
                        COUNT(p.id) as products_count,
                        SUM(p.quantity) as total_quantity,
                        SUM(p.quantity * p.selling_price) as total_value
                    FROM products p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1
                    GROUP BY p.category_id
                    ORDER BY total_value DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $categoryStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => [
                    'summary' => $summary,
                    'low_stock_products' => $lowStockProducts,
                    'category_stats' => $categoryStats
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء تقرير المخزون: ' . $e->getMessage()
            ];
        }
    }

    public function getFinancialReport($startDate, $endDate) {
        try {
            // إجمالي المبيعات والمشتريات
            $sql = "SELECT 
                        (SELECT COALESCE(SUM(final_amount), 0) FROM sales_invoices WHERE DATE(created_at) BETWEEN :start_date AND :end_date) as total_sales,
                        (SELECT COALESCE(SUM(final_amount), 0) FROM purchase_invoices WHERE DATE(created_at) BETWEEN :start_date2 AND :end_date2) as total_purchases";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':start_date', $startDate);
            $stmt->bindParam(':end_date', $endDate);
            $stmt->bindParam(':start_date2', $startDate);
            $stmt->bindParam(':end_date2', $endDate);
            $stmt->execute();
            $summary = $stmt->fetch(PDO::FETCH_ASSOC);

            $summary['profit'] = $summary['total_sales'] - $summary['total_purchases'];

            // ديون العملاء
            $sql = "SELECT 
                        c.name as customer_name,
                        c.code as customer_code,
                        c.balance as debt_amount,
                        c.phone
                    FROM customers c
                    WHERE c.is_active = 1 AND c.balance > 0
                    ORDER BY c.balance DESC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $customerDebts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // ديون الموردين
            $sql = "SELECT 
                        s.name as supplier_name,
                        s.code as supplier_code,
                        s.balance as debt_amount,
                        s.phone
                    FROM suppliers s
                    WHERE s.is_active = 1 AND s.balance < 0
                    ORDER BY s.balance ASC";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $supplierDebts = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'data' => [
                    'summary' => $summary,
                    'customer_debts' => $customerDebts,
                    'supplier_debts' => $supplierDebts
                ]
            ];
        } catch (PDOException $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إنشاء التقرير المالي: ' . $e->getMessage()
            ];
        }
    }
}

// معالجة الطلبات
$api = new ReportsAPI();
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    $reportType = $_GET['type'] ?? 'dashboard';
    
    switch ($reportType) {
        case 'dashboard':
            echo json_encode($api->getDashboardStats());
            break;
            
        case 'sales':
            $startDate = $_GET['start_date'] ?? date('Y-m-01');
            $endDate = $_GET['end_date'] ?? date('Y-m-d');
            echo json_encode($api->getSalesReport($startDate, $endDate));
            break;
            
        case 'inventory':
            echo json_encode($api->getInventoryReport());
            break;
            
        case 'financial':
            $startDate = $_GET['start_date'] ?? date('Y-m-01');
            $endDate = $_GET['end_date'] ?? date('Y-m-d');
            echo json_encode($api->getFinancialReport($startDate, $endDate));
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'نوع التقرير غير مدعوم']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
}
?>