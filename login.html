<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المخزون</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-header .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 32px;
        }

        .login-header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px;
            padding-right: 50px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 16px;
        }

        .form-group.has-label .form-icon {
            top: calc(50% + 12px);
        }

        .btn-login {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
        }

        .remember-me input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #667eea;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .demo-credentials {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .demo-credentials h4 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .demo-credentials p {
            color: #6c757d;
            font-size: 13px;
            margin: 5px 0;
        }

        .demo-credentials code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .login-header h1 {
                font-size: 20px;
            }
            
            .form-control {
                padding: 12px 15px;
                padding-right: 45px;
            }
        }

        /* Animation for container */
        .login-container {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-store"></i>
            </div>
            <h1>نظام إدارة المخزون</h1>
            <p>مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
        </div>

        <div id="alert" class="alert"></div>

        <form id="loginForm">
            <div class="form-group has-label">
                <label class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" required>
                <i class="fas fa-user form-icon"></i>
            </div>

            <div class="form-group has-label">
                <label class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required>
                <i class="fas fa-lock form-icon"></i>
            </div>

            <div class="remember-forgot">
                <label class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <span>تذكرني</span>
                </label>
                <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
            </div>

            <button type="submit" class="btn-login" id="loginBtn">
                <span class="loading" id="loading"></span>
                <span id="loginText">تسجيل الدخول</span>
            </button>
        </form>

        <div class="demo-credentials">
            <h4>بيانات تجريبية للدخول:</h4>
            <p>اسم المستخدم: <code>admin</code></p>
            <p>كلمة المرور: <code>admin123</code></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');
            const loginText = document.getElementById('loginText');
            const alert = document.getElementById('alert');

            // التحقق من وجود جلسة مفتوحة
            if (localStorage.getItem('user_logged_in')) {
                window.location.href = 'index.html';
            }

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const remember = document.getElementById('remember').checked;

                if (!username || !password) {
                    showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                    return;
                }

                // إظهار حالة التحميل
                loginBtn.disabled = true;
                loading.style.display = 'inline-block';
                loginText.textContent = 'جاري تسجيل الدخول...';

                // محاكاة طلب تسجيل الدخول
                setTimeout(() => {
                    // التحقق من البيانات (يمكن استبدالها بطلب API حقيقي)
                    if (username === 'admin' && password === 'admin123') {
                        // نجح تسجيل الدخول
                        showAlert('تم تسجيل الدخول بنجاح!', 'success');
                        
                        // حفظ حالة تسجيل الدخول
                        localStorage.setItem('user_logged_in', 'true');
                        localStorage.setItem('username', username);
                        
                        if (remember) {
                            localStorage.setItem('remember_user', 'true');
                        }

                        // الانتقال إلى الصفحة الرئيسية
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    } else {
                        // فشل تسجيل الدخول
                        showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
                        
                        // إعادة تعيين النموذج
                        loginBtn.disabled = false;
                        loading.style.display = 'none';
                        loginText.textContent = 'تسجيل الدخول';
                    }
                }, 1500);
            });

            function showAlert(message, type) {
                alert.textContent = message;
                alert.className = `alert alert-${type}`;
                alert.style.display = 'block';
                
                // إخفاء التنبيه بعد 5 ثوان
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            }

            // ملء البيانات المحفوظة
            if (localStorage.getItem('remember_user')) {
                document.getElementById('username').value = localStorage.getItem('username') || '';
                document.getElementById('remember').checked = true;
            }

            // تأثير التركيز على الحقول
            const formControls = document.querySelectorAll('.form-control');
            formControls.forEach(control => {
                control.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                control.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });

            // معالجة رابط "نسيت كلمة المرور"
            document.querySelector('.forgot-password').addEventListener('click', function(e) {
                e.preventDefault();
                showAlert('يرجى التواصل مع المدير لإعادة تعيين كلمة المرور', 'error');
            });
        });
    </script>
</body>
</html>