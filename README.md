# نظام إدارة المخزون والمبيعات

نظام شامل لإدارة المخزون والمبيعات مصمم خصيصاً للشركات الصغيرة والمتوسطة.

## المميزات الرئيسية

### 1. لوحة التحكم الرئيسية
- عرض إحصائيات فورية للمبيعات والمخزون
- رسوم بيانية تفاعلية لحركة المبيعات
- تنبيهات ذكية لانخفاض المخزون والفواتير المستحقة

### 2. إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع الكميات والأسعار
- تنبيهات تلقائية عند انخفاض المخزون
- تصنيف المنتجات حسب الفئات

### 3. إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع أرصدة العملاء والديون
- تقارير مفصلة لكل عميل
- بطاقات عملاء قابلة للطباعة

### 4. إدارة الموردين
- معلومات كاملة عن الموردين
- تتبع المدفوعات والمستحقات
- تقييم أداء الموردين

### 5. نظام الفواتير
- إنشاء فواتير مبيعات ومشتريات
- حفظ وطباعة الفواتير
- أرشيف تفاعلي للفواتير السابقة

### 6. التقارير المتقدمة
- تقارير العملاء والموردين
- تقارير المبيعات والأرباح
- تصدير التقارير إلى PDF و Excel

### 7. إدارة المستخدمين
- نظام صلاحيات متقدم
- إدارة حسابات الموظفين
- تتبع العمليات والأنشطة

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- المتصفحات الحديثة

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r * /var/www/html/inventory-system/
```

### 2. إعداد قاعدة البيانات
1. افتح المتصفح واذهب إلى: `http://localhost/inventory-system/setup.php`
2. اتبع تعليمات معالج التثبيت
3. سيتم إنشاء قاعدة البيانات والجداول تلقائياً

### 3. تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 4. تخصيص الإعدادات
- اذهب إلى صفحة الإعدادات لتخصيص بيانات الشركة
- قم بتغيير كلمة مرور المدير
- اضبط إعدادات الضرائب والعملة

## هيكل المشروع

```
inventory-system/
├── index.html              # الصفحة الرئيسية
├── setup.php              # معالج التثبيت
├── README.md              # دليل المستخدم
├── assets/                # الملفات الثابتة
│   ├── css/
│   │   └── style.css      # ملف الأنماط الرئيسي
│   └── js/
│       └── main.js        # ملف JavaScript الرئيسي
├── config/                # ملفات الإعداد
│   └── database.php       # إعدادات قاعدة البيانات
├── api/                   # واجهات برمجة التطبيقات
│   ├── products.php       # API المنتجات
│   ├── customers.php      # API العملاء
│   ├── suppliers.php      # API الموردين
│   ├── sales.php          # API المبيعات
│   └── reports.php        # API التقارير
└── uploads/               # مجلد الملفات المرفوعة
```

## استخدام النظام

### إدارة المنتجات
1. اذهب إلى صفحة "المخزون"
2. انقر على "إضافة منتج جديد"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إنشاء فاتورة مبيعات
1. اذهب إلى صفحة "المبيعات"
2. اختر العميل
3. أضف المنتجات المطلوبة
4. احفظ واطبع الفاتورة

### عرض التقارير
1. اذهب إلى صفحة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اعرض أو صدّر التقرير

## الدعم الفني

### المشاكل الشائعة

**مشكلة: لا يمكن الاتصال بقاعدة البيانات**
- تأكد من صحة بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL

**مشكلة: الصفحة لا تظهر بشكل صحيح**
- تأكد من تفعيل JavaScript في المتصفح
- امسح ذاكرة التخزين المؤقت للمتصفح

**مشكلة: خطأ في الصلاحيات**
- تأكد من صلاحيات الكتابة على مجلد `uploads`
- تحقق من إعدادات PHP

### تحديث النظام
1. احتفظ بنسخة احتياطية من قاعدة البيانات
2. احتفظ بنسخة من ملف `config/database.php`
3. استبدل الملفات القديمة بالجديدة
4. شغل سكريبت التحديث إن وجد

## الأمان

### نصائح الأمان
- غيّر كلمة مرور المدير الافتراضية
- استخدم كلمات مرور قوية
- قم بعمل نسخ احتياطية دورية
- حدّث النظام بانتظام

### النسخ الاحتياطية
```sql
-- نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p inventory_system > backup.sql

-- استعادة النسخة الاحتياطية
mysql -u username -p inventory_system < backup.sql
```

## التطوير والتخصيص

### إضافة ميزات جديدة
1. أنشئ ملف API جديد في مجلد `api/`
2. أضف الصفحة الجديدة في `index.html`
3. أضف الأنماط المطلوبة في `style.css`
4. أضف الوظائف في `main.js`

### تخصيص التصميم
- عدّل ملف `assets/css/style.css`
- استخدم متغيرات CSS للألوان الرئيسية
- اتبع نمط التصميم الموجود

## الترخيص

هذا النظام مطور للاستخدام التجاري والشخصي.

## معلومات الاتصال

للدعم الفني والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966501234567

---

**نظام إدارة المخزون والمبيعات v1.0**  
تم التطوير بواسطة فريق التطوير المتخصص