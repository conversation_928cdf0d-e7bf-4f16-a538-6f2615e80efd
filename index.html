<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>لوحة إدارة المبيعات — تجريبي</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    :root{
      --bg:#f5f8fb;
      --card:#ffffff;
      --primary:#0b74d1; /* أزرق رئيسي */
      --accent:#ff8a00;  /* برتقالي */
      --muted:#6b7280;
      --success:#16a34a;
      --danger:#dc2626;
      --glass: rgba(255,255,255,0.6);
      --shadow: 0 6px 18px rgba(11,23,40,0.08);
    }
    *{box-sizing:border-box}
    body{
      margin:0;
      font-family:'<PERSON><PERSON><PERSON>',system-ui,Arial,sans-serif;
      background: linear-gradient(180deg,var(--bg) 0%, #eef6ff 100%);
      color:#0f172a;
      -webkit-font-smoothing:antialiased;
      -moz-osx-font-smoothing:grayscale;
      direction:rtl;
    }

    /* layout */
    .app{
      display:grid;
      grid-template-columns: 260px 1fr;
      gap:20px;
      padding:22px;
      min-height:100vh;
      align-items:start;
    }
    @media (max-width:920px){
      .app{grid-template-columns:1fr; padding:12px;}
    }

    /* sidebar */
    .sidebar{
      background:linear-gradient(180deg, rgba(11,116,209,0.95), rgba(6,90,170,0.95));
      color:#fff;
      border-radius:14px;
      padding:18px;
      box-shadow: var(--shadow);
      position:sticky;
      top:18px;
      height:fit-content;
    }
    .brand{
      display:flex;
      gap:12px;
      align-items:center;
      margin-bottom:18px;
    }
    .logo{
      width:44px;
      height:44px;
      background:var(--accent);
      border-radius:10px;
      display:grid;
      place-items:center;
      font-weight:700;
      color:#fff;
      box-shadow: 0 6px 14px rgba(255,138,0,0.18);
    }
    .brand h1{margin:0;font-size:18px;font-weight:700}
    .brand p{margin:0;font-size:12px;opacity:0.95}

    nav ul{list-style:none;padding:0;margin:12px 0}
    nav a{
      display:flex;
      gap:12px;
      align-items:center;
      padding:10px 12px;
      border-radius:10px;
      color:rgba(255,255,255,0.95);
      text-decoration:none;
      font-weight:600;
      margin-bottom:6px;
    }
    nav a:hover, nav a.active{
      background: rgba(255,255,255,0.08);
      box-shadow: inset 0 0 0 1px rgba(255,255,255,0.02);
    }
    .small{
      font-size:12px;color:rgba(255,255,255,0.8);font-weight:500;
    }

    /* main */
    .main{
      padding:10px;
    }
    .topbar{
      display:flex;
      justify-content:space-between;
      gap:12px;
      align-items:center;
      margin-bottom:16px;
    }
    .search{
      display:flex;
      gap:10px;
      align-items:center;
      background:var(--card);
      padding:8px 12px;
      border-radius:12px;
      box-shadow:var(--shadow);
      width:60%;
      min-width:240px;
    }
    .search input{
      border:0; outline:0; font-size:14px; width:100%; direction:ltr;
      font-family:inherit;
    }
    .actions{
      display:flex;
      gap:10px;
      align-items:center;
    }
    .btn{
      background:var(--primary);
      color:#fff;
      padding:9px 12px;
      border-radius:10px;
      border:0;
      font-weight:700;
      cursor:pointer;
      box-shadow: 0 8px 18px rgba(11,116,209,0.12);
    }
    .btn.secondary{
      background:transparent;
      color:var(--muted);
      border:1px solid rgba(15,23,42,0.06);
      box-shadow:none;
      font-weight:600;
    }

    /* stats */
    .grid{
      display:grid;
      grid-template-columns:repeat(4,1fr);
      gap:14px;
      margin-bottom:16px;
    }
    @media (max-width:920px){
      .grid{grid-template-columns:repeat(2,1fr);}
    }
    .card{
      background:var(--card);
      padding:14px;
      border-radius:12px;
      box-shadow:var(--shadow);
    }
    .kpi{
      display:flex;
      justify-content:space-between;
      align-items:center;
      gap:10px;
    }
    .kpi h3{margin:0;font-size:20px}
    .kpi p{margin:4px 0 0;color:var(--muted);font-size:13px}
    .spark{font-weight:700;color:var(--accent)}

    /* main content columns */
    .columns{
      display:grid;
      grid-template-columns: 2fr 1fr;
      gap:14px;
      margin-bottom:16px;
    }
    @media (max-width:1100px){.columns{grid-template-columns:1fr;}}

    /* orders / table */
    .table-wrap{background:var(--card);padding:12px;border-radius:12px;box-shadow:var(--shadow)}
    table{width:100%;border-collapse:collapse;font-size:14px}
    thead th{ text-align:right;padding:12px 8px;color:var(--muted);font-weight:700;font-size:13px}
    tbody td{ padding:10px 8px;border-top:1px solid #f0f2f6}
    .status{
      display:inline-block;padding:6px 8px;border-radius:999px;font-weight:700;font-size:12px;
    }
    .status.paid{background:rgba(22,163,74,0.12);color:var(--success)}
    .status.due{background:rgba(255,138,0,0.12);color:var(--accent)}
    .status.over{background:rgba(220,38,38,0.08);color:var(--danger)}

    /* sidebar cards on right column */
    .mini-card{display:flex;justify-content:space-between;align-items:center;padding:12px;border-radius:10px;margin-bottom:10px}
    .progress{height:8px;background:#eef3fb;border-radius:6px;overflow:hidden}
    .progress > span{display:block;height:100%;border-radius:6px}

    /* footer */
    footer{margin-top:18px;color:var(--muted);font-size:13px;text-align:center;padding:10px}

    /* utilities */
    .muted{color:var(--muted);font-weight:600}
    .right{ text-align:right }
    .left{ text-align:left }
    .chip{padding:6px 10px;border-radius:8px;background:#f2f6ff;font-weight:700;color:var(--primary)}
    .table-actions button{border:0;background:transparent;cursor:pointer;padding:6px;border-radius:8px}
    .table-actions button:hover{background:#f5f7fb}
    .avatar{width:36px;height:36px;border-radius:10px;background:linear-gradient(180deg,#fff,#eef6ff);display:grid;place-items:center;font-weight:700;color:var(--primary);}

    /* small responsive tweaks */
    @media (max-width:560px){
      .search{width:100%}
      .brand h1{font-size:16px}
      .grid{grid-template-columns:repeat(1,1fr)}
    }
  </style>
</head>
<body>
  <div class="app">
    <!-- SIDEBAR -->
    <aside class="sidebar" role="navigation" aria-label="القائمة الرئيسية">
      <div class="brand">
        <div class="logo">CA</div>
        <div>
          <h1>التقويم الزراعي</h1>
          <p class="small">نظام إدارة المبيعات</p>
        </div>
      </div>

      <nav>
        <ul>
          <li><a href="#" class="active">🏠 لوحة التحكم</a></li>
          <li><a href="#">🧾 فواتير المبيعات</a></li>
          <li><a href="#">📦 المخزون</a></li>
          <li><a href="#">👥 العملاء</a></li>
          <li><a href="#">🔁 الموردون</a></li>
          <li><a href="#">📊 التقارير</a></li>
          <li><a href="#">⚙️ الإعدادات</a></li>
        </ul>
      </nav>

      <div style="margin-top:14px">
        <div class="card" style="background:rgba(255,255,255,0.06);padding:12px">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <div class="small">إجمالي ديون الموردين</div>
              <div style="font-size:18px;font-weight:800;margin-top:6px">15,240 د.م</div>
            </div>
            <div style="text-align:left">
              <div class="chip">عرض سريع</div>
            </div>
          </div>
        </div>

        <div style="height:12px"></div>

        <div class="card" style="background:linear-gradient(90deg, rgba(255,138,0,0.12), rgba(255,255,255,0.02));">
          <div class="small">حالة المخزون</div>
          <h3 style="margin:6px 0 0">مستقر</h3>
          <p class="small" style="margin-top:6px">أقرب تنبيه: أبريل 2026</p>
        </div>
      </div>
    </aside>

    <!-- MAIN -->
    <main class="main" role="main">
      <div class="topbar">
        <div class="search" role="search">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" style="opacity:0.6">
            <path d="M21 21l-4.35-4.35" stroke="#0f172a" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="11" cy="11" r="6.2" stroke="#0f172a" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <input placeholder="ابحث عن فاتورة، منتج، مورد..." aria-label="بحث">
          <button class="btn secondary" onclick="alert('ميزة البحث تجريبية')">بحث</button>
        </div>
        <div class="actions">
          <button class="btn" onclick="newInvoice()">+ فاتورة جديدة</button>
          <div style="display:flex;gap:10px;align-items:center">
            <div class="avatar">م</div>
            <div style="text-align:left">
              <div style="font-weight:800">محمد</div>
              <div class="small">المسؤول</div>
            </div>
          </div>
        </div>
      </div>

      <!-- KPIs -->
      <section class="grid" aria-label="الإحصائيات">
        <div class="card">
          <div class="kpi">
            <div>
              <h3>124,560 د.م</h3>
              <p>إجمالي المبيعات (شهري)</p>
            </div>
            <div class="right">
              <div class="spark">+8.4%</div>
              <div class="small">عن الشهر الماضي</div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="kpi">
            <div>
              <h3>3,210</h3>
              <p>عدد الفواتير</p>
            </div>
            <div class="right">
              <div class="spark">+1.6%</div>
              <div class="small">تغيير أسبوعي</div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="kpi">
            <div>
              <h3>78</h3>
              <p>عملاء جدد</p>
            </div>
            <div class="right">
              <div class="spark">+12%</div>
              <div class="small">هذا الشهر</div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="kpi">
            <div>
              <h3>15,240 د.م</h3>
              <p>ديون الموردين</p>
            </div>
            <div class="right">
              <div class="spark">---</div>
              <div class="small">تحديث آلي</div>
            </div>
          </div>
        </div>
      </section>

      <!-- main columns -->
      <section class="columns">
        <!-- left: جدول الفواتير -->
        <div>
          <div class="table-wrap card" style="margin-bottom:12px">
            <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:10px">
              <div>
                <h2 style="margin:0;font-size:18px">أحدث الفواتير</h2>
                <p class="small" style="margin:6px 0 0">قائمة بآخر 8 فواتير — تحديث مباشر</p>
              </div>
              <div class="small">تصفية: <strong>هذا الشهر</strong></div>
            </div>

            <table aria-label="جدول الفواتير">
              <thead>
                <tr>
                  <th>العميل</th>
                  <th>رقم الفاتورة</th>
                  <th>المبلغ</th>
                  <th>تاريخ</th>
                  <th>الحالة</th>
                  <th style="text-align:left">إجراءات</th>
                </tr>
              </thead>
              <tbody id="invoices">
                <tr>
                  <td class="right">مزارع الأطلس</td>
                  <td class="right">INV-2041</td>
                  <td class="right">5,400 د.م</td>
                  <td class="right">2025-07-30</td>
                  <td class="right"><span class="status paid">مدفوعة</span></td>
                  <td class="left table-actions">
                    <button title="عرض" onclick="alert('عرض الفاتورة INV-2041')">🔍</button>
                    <button title="طباعة" onclick="alert('طباعة')">🖨️</button>
                    <button title="حذف" onclick="confirmDelete('INV-2041')">🗑️</button>
                  </td>
                </tr>
                <tr>
                  <td class="right">تاجر الخضر</td>
                  <td class="right">INV-2039</td>
                  <td class="right">2,100 د.م</td>
                  <td class="right">2025-07-27</td>
                  <td class="right"><span class="status due">مطلوب</span></td>
                  <td class="left table-actions">
                    <button title="عرض">🔍</button>
                    <button title="تذكير" onclick="alert('تم ارسال تذكير')">✉️</button>
                  </td>
                </tr>
                <tr>
                  <td class="right">محل الريف</td>
                  <td class="right">INV-2035</td>
                  <td class="right">9,720 د.م</td>
                  <td class="right">2025-07-10</td>
                  <td class="right"><span class="status over">متأخرة</span></td>
                  <td class="left table-actions">
                    <button title="عرض">🔍</button>
                    <button title="متابعة" onclick="alert('متابعة المبلغ')">📞</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="card">
            <h3 style="margin:0 0 8px">ملاحظات سريعة</h3>
            <ul style="margin:0;padding-inline-start:16px">
              <li class="small">تحديث أسعار الأسمدة الأسبوع القادم.</li>
              <li class="small">فحص مخزون الطماطم قبل شهر أغسطس.</li>
            </ul>
          </div>
        </div>

        <!-- right: ملخص الموردين وادوات -->
        <aside>
          <div class="card mini-card" style="background:linear-gradient(90deg,#fff,#f8fbff);">
            <div>
              <div class="small">الموردون النشطون</div>
              <div style="font-weight:800;font-size:20px;margin-top:6px">24</div>
            </div>
            <div class="muted">≥ 90% نشاط</div>
          </div>

          <div class="card">
            <h4 style="margin:0 0 8px">أعلى 5 موردين (ديون)</h4>
            <ol style="margin:0;padding-inline-start:14px">
              <li class="small">شركة أ — 5,000 د.م</li>
              <li class="small">شركة ب — 3,200 د.م</li>
              <li class="small">شركة ج — 2,400 د.م</li>
              <li class="small">شركة د — 1,840 د.م</li>
              <li class="small">شركة هـ — 800 د.م</li>
            </ol>
          </div>

          <div class="card" style="margin-top:10px">
            <h4 style="margin:0 0 8px">أدوات سريعة</h4>
            <div style="display:flex;flex-direction:column;gap:8px">
              <button class="btn" onclick="alert('إضافة مورد')">+ إضافة مورد</button>
              <button class="btn secondary" onclick="alert('تصدير CSV')">تصدير CSV</button>
              <button class="btn secondary" onclick="alert('استيراد بيانات')">استيراد</button>
            </div>
          </div>
        </aside>
      </section>

      <footer>
        نسخة تجريبية — تصميم واجهة متجاوبة للاختبار — © التقويم الزراعي
      </footer>
    </main>
  </div>

  <script>
    // تفاعلية خفيفة لأغراض الاختبار
    function newInvoice(){
      const num = Math.floor(Math.random()*9000) + 1000;
      alert('إنشاء فاتورة جديدة رقم: INV-' + num);
    }
    function confirmDelete(id){
      if(confirm('هل تريد حذف الفاتورة ' + id + '؟')) {
        alert('تم الحذف (تجريبي)');
      }
    }

    // إضافة سطر جديد تجريبي إلى جدول الفواتير (للاختبار)
    (function addSampleRow(){
      // سطر تجريبي يظهر بعد 1.2 ثانية
      setTimeout(()=>{
        const tbody = document.getElementById('invoices');
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td class="right">مشتري تجريبي</td>
          <td class="right">INV-` + (Math.floor(Math.random()*900)+300) + `</td>
          <td class="right">1,200 د.م</td>
          <td class="right">2025-08-01</td>
          <td class="right"><span class="status due">مطلوب</span></td>
          <td class="left table-actions">
            <button title="عرض" onclick="alert('عرض')">🔍</button>
            <button title="تذكير" onclick="alert('تذكير')">✉️</button>
          </td>
        `;
        tbody.prepend(tr);
      }, 1200);
    })();
  </script>
</body>
</html>
