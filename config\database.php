<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'inventory_system');
define('DB_USER', 'root');
define('DB_PASS', '');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $conn;

    // الاتصال بقاعدة البيانات
    public function connect() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $e) {
            echo "خطأ في الاتصال: " . $e->getMessage();
        }

        return $this->conn;
    }

    // إنشاء قاعدة البيانات والجداول
    public function createDatabase() {
        try {
            // الاتصال بدون تحديد قاعدة البيانات
            $conn = new PDO(
                "mysql:host=" . $this->host . ";charset=utf8mb4",
                $this->username,
                $this->password
            );
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $conn->exec($sql);

            // الاتصال بقاعدة البيانات الجديدة
            $this->conn = $this->connect();

            // إنشاء الجداول
            $this->createTables();

            return true;
        } catch(PDOException $e) {
            echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
            return false;
        }
    }

    // إنشاء الجداول
    private function createTables() {
        $tables = [
            // جدول المستخدمين
            "users" => "
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    email VARCHAR(100),
                    phone VARCHAR(20),
                    role ENUM('admin', 'manager', 'employee') DEFAULT 'employee',
                    permissions JSON,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول الفئات
            "categories" => "
                CREATE TABLE IF NOT EXISTS categories (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول المنتجات
            "products" => "
                CREATE TABLE IF NOT EXISTS products (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(50) UNIQUE NOT NULL,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    category_id INT,
                    quantity INT DEFAULT 0,
                    min_stock INT DEFAULT 0,
                    cost_price DECIMAL(10,2) DEFAULT 0.00,
                    selling_price DECIMAL(10,2) DEFAULT 0.00,
                    barcode VARCHAR(100),
                    image VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول العملاء
            "customers" => "
                CREATE TABLE IF NOT EXISTS customers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(50) UNIQUE NOT NULL,
                    name VARCHAR(100) NOT NULL,
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    city VARCHAR(50),
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    credit_limit DECIMAL(10,2) DEFAULT 0.00,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول الموردين
            "suppliers" => "
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    code VARCHAR(50) UNIQUE NOT NULL,
                    name VARCHAR(100) NOT NULL,
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    city VARCHAR(50),
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    rating INT DEFAULT 0,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول فواتير المبيعات
            "sales_invoices" => "
                CREATE TABLE IF NOT EXISTS sales_invoices (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_number VARCHAR(50) UNIQUE NOT NULL,
                    customer_id INT,
                    user_id INT,
                    invoice_date DATE NOT NULL,
                    due_date DATE,
                    subtotal DECIMAL(10,2) DEFAULT 0.00,
                    tax_amount DECIMAL(10,2) DEFAULT 0.00,
                    discount_amount DECIMAL(10,2) DEFAULT 0.00,
                    total_amount DECIMAL(10,2) DEFAULT 0.00,
                    paid_amount DECIMAL(10,2) DEFAULT 0.00,
                    payment_status ENUM('paid', 'partial', 'unpaid') DEFAULT 'unpaid',
                    payment_method ENUM('cash', 'credit', 'bank_transfer', 'check') DEFAULT 'cash',
                    notes TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول تفاصيل فواتير المبيعات
            "sales_invoice_items" => "
                CREATE TABLE IF NOT EXISTS sales_invoice_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL,
                    unit_price DECIMAL(10,2) NOT NULL,
                    total_price DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول فواتير المشتريات
            "purchase_invoices" => "
                CREATE TABLE IF NOT EXISTS purchase_invoices (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_number VARCHAR(50) UNIQUE NOT NULL,
                    supplier_id INT,
                    user_id INT,
                    invoice_date DATE NOT NULL,
                    due_date DATE,
                    subtotal DECIMAL(10,2) DEFAULT 0.00,
                    tax_amount DECIMAL(10,2) DEFAULT 0.00,
                    discount_amount DECIMAL(10,2) DEFAULT 0.00,
                    total_amount DECIMAL(10,2) DEFAULT 0.00,
                    paid_amount DECIMAL(10,2) DEFAULT 0.00,
                    payment_status ENUM('paid', 'partial', 'unpaid') DEFAULT 'unpaid',
                    payment_method ENUM('cash', 'credit', 'bank_transfer', 'check') DEFAULT 'cash',
                    notes TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول تفاصيل فواتير المشتريات
            "purchase_invoice_items" => "
                CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    invoice_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL,
                    unit_price DECIMAL(10,2) NOT NULL,
                    total_price DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (invoice_id) REFERENCES purchase_invoices(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول المدفوعات
            "payments" => "
                CREATE TABLE IF NOT EXISTS payments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    payment_number VARCHAR(50) UNIQUE NOT NULL,
                    type ENUM('customer_payment', 'supplier_payment') NOT NULL,
                    customer_id INT NULL,
                    supplier_id INT NULL,
                    user_id INT,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_method ENUM('cash', 'bank_transfer', 'check') DEFAULT 'cash',
                    payment_date DATE NOT NULL,
                    reference_number VARCHAR(100),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",

            // جدول الإعدادات
            "settings" => "
                CREATE TABLE IF NOT EXISTS settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->conn->exec($sql);
                echo "تم إنشاء جدول $tableName بنجاح<br>";
            } catch(PDOException $e) {
                echo "خطأ في إنشاء جدول $tableName: " . $e->getMessage() . "<br>";
            }
        }

        // إدراج البيانات الأولية
        $this->insertInitialData();
    }

    // إدراج البيانات الأولية
    private function insertInitialData() {
        try {
            // إدراج مستخدم افتراضي (admin)
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $sql = "INSERT IGNORE INTO users (username, password, full_name, role) VALUES ('admin', ?, 'المدير العام', 'admin')";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$hashedPassword]);

            // إدراج فئات افتراضية
            $categories = [
                ['إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية'],
                ['ملابس', 'ملابس رجالية ونسائية وأطفال'],
                ['أدوات منزلية', 'أدوات ومعدات منزلية'],
                ['كتب وقرطاسية', 'كتب ومواد قرطاسية']
            ];

            foreach ($categories as $category) {
                $sql = "INSERT IGNORE INTO categories (name, description) VALUES (?, ?)";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute($category);
            }

            // إدراج إعدادات افتراضية
            $settings = [
                ['company_name', 'شركة إدارة المخزون', 'اسم الشركة'],
                ['company_phone', '0501234567', 'هاتف الشركة'],
                ['company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'],
                ['company_address', 'الرياض، المملكة العربية السعودية', 'عنوان الشركة'],
                ['currency', 'ريال', 'العملة المستخدمة'],
                ['tax_rate', '15', 'معدل الضريبة %'],
                ['low_stock_alert', '10', 'تنبيه انخفاض المخزون']
            ];

            foreach ($settings as $setting) {
                $sql = "INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute($setting);
            }

            echo "تم إدراج البيانات الأولية بنجاح<br>";
        } catch(PDOException $e) {
            echo "خطأ في إدراج البيانات الأولية: " . $e->getMessage() . "<br>";
        }
    }
}
?>