<?php
require_once '../config/database.php';

class BarcodeAPI {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    // توليد كود بار جديد
    public function generateBarcode($type = 'EAN13') {
        try {
            do {
                if ($type === 'EAN13') {
                    $barcode = $this->generateEAN13();
                } else {
                    $barcode = $this->generateCode128();
                }
                
                // التحقق من عدم وجود الكود مسبق<|im_start|>
                $sql = "SELECT id FROM products WHERE barcode = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$barcode]);
                $exists = $stmt->fetch();
                
            } while ($exists);
            
            return [
                'success' => true,
                'barcode' => $barcode,
                'type' => $type
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في توليد الكود بار: ' . $e->getMessage()
            ];
        }
    }
    
    // التحقق من صحة الكود بار
    public function validateBarcode($barcode) {
        $length = strlen($barcode);
        
        if ($length === 13) {
            return $this->validateEAN13($barcode);
        } elseif ($length >= 6 && $length <= 20) {
            return ['valid' => true, 'type' => 'CODE128'];
        } else {
            return ['valid' => false, 'message' => 'طول الكود بار غير صحيح'];
        }
    }
    
    private function generateEAN13() {
        $code = '';
        for ($i = 0; $i < 12; $i++) {
            $code .= rand(0, 9);
        }
        
        $checksum = $this->calculateEAN13Checksum($code);
        return $code . $checksum;
    }
    
    private function generateCode128() {
        return str_pad(rand(100000, 999999999999), 12, '0', STR_PAD_LEFT);
    }
    
    private function calculateEAN13Checksum($code) {
        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $digit = (int)$code[$i];
            $sum += ($i % 2 === 0) ? $digit : $digit * 3;
        }
        return (10 - ($sum % 10)) % 10;
    }
    
    private function validateEAN13($barcode) {
        if (strlen($barcode) !== 13 || !ctype_digit($barcode)) {
            return ['valid' => false, 'message' => 'كود EAN-13 يجب أن يكون 13 رقم'];
        }
        
        $code = substr($barcode, 0, 12);
        $checksum = (int)substr($barcode, 12, 1);
        $calculatedChecksum = $this->calculateEAN13Checksum($code);
        
        if ($checksum === $calculatedChecksum) {
            return ['valid' => true, 'type' => 'EAN13'];
        } else {
            return ['valid' => false, 'message' => 'رقم التحقق غير صحيح'];
        }
    }
}

// معالجة الطلبات
$barcodeAPI = new BarcodeAPI();
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['generate'])) {
            $type = $_GET['type'] ?? 'EAN13';
            $response = $barcodeAPI->generateBarcode($type);
        } elseif (isset($_GET['validate'])) {
            $barcode = $_GET['barcode'] ?? '';
            $response = $barcodeAPI->validateBarcode($barcode);
        } else {
            $response = ['success' => false, 'message' => 'معامل مطلوب'];
        }
        break;
        
    default:
        $response = ['success' => false, 'message' => 'طريقة غير مدعومة'];
        break;
}

header('Content-Type: application/json; charset=utf-8');
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>